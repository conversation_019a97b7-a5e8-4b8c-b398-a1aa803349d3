<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快乐学汉字 - 大班认字游戏</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+SC:wght@300;400;500;700;900&display=swap" rel="stylesheet">
    <meta name="description" content="专为5-6岁大班小朋友设计的互动汉字学习游戏，包含看图识字、拼音识字、记忆配对三种模式">
    <meta name="keywords" content="汉字学习,幼儿教育,认字游戏,拼音,记忆游戏">
    <link rel="icon" href="data:image/svg+xml,<svg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 100 100'><text y='.9em' font-size='90'>📚</text></svg>">
</head>
<body>
    <div class="container">
        <!-- 顶部装饰 -->
        <div class="top-decoration">
            <div class="cloud cloud-1">☁️</div>
            <div class="cloud cloud-2">☁️</div>
            <div class="cloud cloud-3">☁️</div>
        </div>

        <header class="header">
            <div class="header-content">
                <h1>🌟 快乐学汉字 🌟</h1>
                <p class="subtitle">专为大班小朋友设计的认字游戏</p>
                <div class="score-container">
                    <div class="score">
                        <span class="score-label">得分:</span>
                        <span id="score" class="score-value">0</span>
                        <div class="score-stars">
                            <span class="star" id="star1">⭐</span>
                            <span class="star" id="star2">⭐</span>
                            <span class="star" id="star3">⭐</span>
                        </div>
                    </div>
                    <div class="level-indicator">
                        <span>等级: </span>
                        <span id="levelText">新手</span>
                    </div>
                </div>
            </div>
        </header>

        <main class="main-content">
            <!-- 游戏模式选择 -->
            <div id="gameMenu" class="game-menu">
                <div class="menu-header">
                    <h2>选择游戏模式</h2>
                    <p class="menu-description">选择你最喜欢的学习方式开始游戏吧！</p>
                </div>
                <div class="menu-buttons">
                    <button class="menu-btn picture-btn" onclick="startGame('picture')">
                        <div class="btn-icon">🖼️</div>
                        <div class="btn-title">看图识字</div>
                        <div class="btn-description">通过图片学习汉字</div>
                        <div class="btn-level">适合: 初学者</div>
                    </button>
                    <button class="menu-btn pinyin-btn" onclick="startGame('pinyin')">
                        <div class="btn-icon">🎵</div>
                        <div class="btn-title">拼音识字</div>
                        <div class="btn-description">听拼音选汉字</div>
                        <div class="btn-level">适合: 进阶者</div>
                    </button>
                    <button class="menu-btn memory-btn" onclick="startGame('memory')">
                        <div class="btn-icon">🧠</div>
                        <div class="btn-title">记忆配对</div>
                        <div class="btn-description">汉字与含义配对</div>
                        <div class="btn-level">适合: 挑战者</div>
                    </button>
                    <button class="menu-btn challenge-btn" onclick="startGame('challenge')">
                        <div class="btn-icon">🏆</div>
                        <div class="btn-title">综合挑战</div>
                        <div class="btn-description">混合题型挑战</div>
                        <div class="btn-level">适合: 高手</div>
                    </button>
                </div>
                
                <!-- 学习进度 -->
                <div class="progress-section">
                    <h3>学习进度</h3>
                    <div class="progress-grid">
                        <div class="progress-item">
                            <div class="progress-icon">🖼️</div>
                            <div class="progress-info">
                                <span class="progress-title">看图识字</span>
                                <div class="progress-bar">
                                    <div class="progress-fill" id="pictureProgress" style="width: 0%"></div>
                                </div>
                                <span class="progress-text" id="pictureProgressText">0/10</span>
                            </div>
                        </div>
                        <div class="progress-item">
                            <div class="progress-icon">🎵</div>
                            <div class="progress-info">
                                <span class="progress-title">拼音识字</span>
                                <div class="progress-bar">
                                    <div class="progress-fill" id="pinyinProgress" style="width: 0%"></div>
                                </div>
                                <span class="progress-text" id="pinyinProgressText">0/10</span>
                            </div>
                        </div>
                        <div class="progress-item">
                            <div class="progress-icon">🧠</div>
                            <div class="progress-info">
                                <span class="progress-title">记忆配对</span>
                                <div class="progress-bar">
                                    <div class="progress-fill" id="memoryProgress" style="width: 0%"></div>
                                </div>
                                <span class="progress-text" id="memoryProgressText">0/6</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 看图识字游戏 -->
            <div id="pictureGame" class="game-area hidden">
                <div class="game-header">
                    <button class="back-btn" onclick="backToMenu()">← 返回</button>
                    <div class="game-title">
                        <h3>🖼️ 看图识字</h3>
                        <p class="game-subtitle">仔细观察图片，选择正确的汉字</p>
                    </div>
                    <div class="progress-info">
                        <div class="progress">第 <span id="currentQuestion">1</span> / 10 题</div>
                        <div class="time-limit" id="timeLimit">⏱️ 30s</div>
                    </div>
                </div>
                
                <div class="picture-container">
                    <div class="picture-box">
                        <img id="questionImage" src="" alt="题目图片">
                        <div class="image-hint" id="imageHint">点击图片听发音</div>
                    </div>
                    <div class="question-text">
                        <span class="question-icon">❓</span>
                        <span>这是什么？</span>
                    </div>
                </div>

                <div class="answer-options">
                    <button class="answer-btn" onclick="selectAnswer(0)">
                        <span class="answer-text"></span>
                        <span class="answer-pinyin"></span>
                    </button>
                    <button class="answer-btn" onclick="selectAnswer(1)">
                        <span class="answer-text"></span>
                        <span class="answer-pinyin"></span>
                    </button>
                    <button class="answer-btn" onclick="selectAnswer(2)">
                        <span class="answer-text"></span>
                        <span class="answer-pinyin"></span>
                    </button>
                </div>

                <div class="game-controls">
                    <button id="hintBtn" class="hint-btn" onclick="showHint()">💡 提示</button>
                    <button id="nextBtn" class="next-btn hidden" onclick="nextQuestion()">下一题 →</button>
                </div>
            </div>

            <!-- 拼音识字游戏 -->
            <div id="pinyinGame" class="game-area hidden">
                <div class="game-header">
                    <button class="back-btn" onclick="backToMenu()">← 返回</button>
                    <div class="game-title">
                        <h3>🎵 拼音识字</h3>
                        <p class="game-subtitle">听拼音，选择正确的汉字</p>
                    </div>
                    <div class="progress-info">
                        <div class="progress">第 <span id="pinyinCurrentQuestion">1</span> / 10 题</div>
                        <div class="time-limit" id="pinyinTimeLimit">⏱️ 30s</div>
                    </div>
                </div>

                <div class="pinyin-container">
                    <div class="pinyin-display">
                        <div class="pinyin-text" id="pinyinText">mā ma</div>
                        <button class="speak-btn" onclick="speakPinyin()">
                            <span class="speaker-icon">🔊</span>
                            <span class="speaker-text">点击听发音</span>
                        </button>
                        <div class="auto-play-indicator" id="autoPlayIndicator">🎵 自动播放中...</div>
                    </div>
                    <div class="question-text">
                        <span class="question-icon">👂</span>
                        <span>选择正确的汉字</span>
                    </div>
                </div>

                <div class="answer-options">
                    <button class="answer-btn large-text" onclick="selectPinyinAnswer(0)">
                        <span class="answer-char"></span>
                        <span class="answer-meaning"></span>
                    </button>
                    <button class="answer-btn large-text" onclick="selectPinyinAnswer(1)">
                        <span class="answer-char"></span>
                        <span class="answer-meaning"></span>
                    </button>
                    <button class="answer-btn large-text" onclick="selectPinyinAnswer(2)">
                        <span class="answer-char"></span>
                        <span class="answer-meaning"></span>
                    </button>
                </div>

                <div class="game-controls">
                    <button id="pinyinHintBtn" class="hint-btn" onclick="showPinyinHint()">💡 提示</button>
                    <button id="pinyinNextBtn" class="next-btn hidden" onclick="nextPinyinQuestion()">下一题 →</button>
                </div>
            </div>

            <!-- 记忆配对游戏 -->
            <div id="memoryGame" class="game-area hidden">
                <div class="game-header">
                    <button class="back-btn" onclick="backToMenu()">← 返回</button>
                    <div class="game-title">
                        <h3>🧠 记忆配对</h3>
                        <p class="game-subtitle">找到汉字与含义的配对</p>
                    </div>
                    <div class="progress-info">
                        <div class="progress">找到 <span id="foundPairs">0</span> / 6 对</div>
                        <div class="time-display" id="memoryTime">⏱️ 00:00</div>
                    </div>
                </div>

                <div class="memory-stats">
                    <div class="stat-item">
                        <span class="stat-icon">🎯</span>
                        <span class="stat-label">准确率:</span>
                        <span class="stat-value" id="accuracyRate">100%</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-icon">🔄</span>
                        <span class="stat-label">翻牌次数:</span>
                        <span class="stat-value" id="flipCount">0</span>
                    </div>
                </div>

                <div class="memory-grid" id="memoryGrid">
                    <!-- 卡片将通过JavaScript生成 -->
                </div>

                <div class="game-controls">
                    <button class="hint-btn" onclick="showMemoryHint()">💡 提示</button>
                    <button class="restart-btn" onclick="restartMemoryGame()">🔄 重新开始</button>
                </div>
            </div>

            <!-- 综合挑战游戏 -->
            <div id="challengeGame" class="game-area hidden">
                <div class="game-header">
                    <button class="back-btn" onclick="backToMenu()">← 返回</button>
                    <div class="game-title">
                        <h3>🏆 综合挑战</h3>
                        <p class="game-subtitle">混合题型，全面测试你的汉字能力</p>
                    </div>
                    <div class="progress-info">
                        <div class="progress">第 <span id="challengeCurrentQuestion">1</span> / 15 题</div>
                        <div class="challenge-score">挑战得分: <span id="challengeScore">0</span></div>
                    </div>
                </div>

                <div class="challenge-type-indicator">
                    <span class="type-icon" id="challengeTypeIcon">🖼️</span>
                    <span class="type-text" id="challengeTypeText">看图识字题</span>
                </div>

                <div id="challengeContent" class="challenge-content">
                    <!-- 内容将根据题型动态生成 -->
                </div>

                <div class="game-controls">
                    <button id="challengeHintBtn" class="hint-btn" onclick="showChallengeHint()">💡 提示</button>
                    <button id="challengeNextBtn" class="next-btn hidden" onclick="nextChallengeQuestion()">下一题 →</button>
                </div>
            </div>

            <!-- 游戏结果 -->
            <div id="gameResult" class="result-area hidden">
                <div class="result-content">
                    <div class="result-animation">
                        <div class="result-icon animate">🎉</div>
                        <div class="celebration-particles">
                            <span class="particle">⭐</span>
                            <span class="particle">🌟</span>
                            <span class="particle">✨</span>
                            <span class="particle">💫</span>
                            <span class="particle">🎊</span>
                            <span class="particle">🎈</span>
                        </div>
                    </div>
                    <h2 id="resultTitle">太棒了！</h2>
                    <div class="result-details">
                        <div class="result-score-big">
                            <span class="score-label">总得分</span>
                            <span id="finalScore" class="score-number">0</span>
                        </div>
                        <div class="result-stats">
                            <div class="stat-box">
                                <div class="stat-icon">🎯</div>
                                <div class="stat-text">
                                    <span class="stat-number" id="correctCount">0</span>
                                    <span class="stat-label">答对题数</span>
                                </div>
                            </div>
                            <div class="stat-box">
                                <div class="stat-icon">⏱️</div>
                                <div class="stat-text">
                                    <span class="stat-number" id="totalTime">0:00</span>
                                    <span class="stat-label">用时</span>
                                </div>
                            </div>
                            <div class="stat-box">
                                <div class="stat-icon">📈</div>
                                <div class="stat-text">
                                    <span class="stat-number" id="accuracyPercent">100%</span>
                                    <span class="stat-label">准确率</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="result-message" id="resultMessage">继续加油！</div>
                    <div class="achievement-section" id="achievementSection">
                        <h4>🏅 获得成就</h4>
                        <div class="achievements" id="achievements">
                            <!-- 成就将动态显示 -->
                        </div>
                    </div>
                    <div class="result-buttons">
                        <button class="result-btn primary" onclick="restartCurrentGame()">
                            <span class="btn-icon">🔄</span>
                            <span>再来一次</span>
                        </button>
                        <button class="result-btn secondary" onclick="backToMenu()">
                            <span class="btn-icon">🏠</span>
                            <span>返回主页</span>
                        </button>
                        <button class="result-btn share" onclick="shareResult()">
                            <span class="btn-icon">📤</span>
                            <span>分享成绩</span>
                        </button>
                    </div>
                </div>
            </div>
        </main>

        <!-- 成功提示 -->
        <div id="successPopup" class="popup hidden">
            <div class="popup-content success">
                <div class="popup-animation">
                    <div class="popup-icon bounce">⭐</div>
                    <div class="success-particles">
                        <span class="particle">✨</span>
                        <span class="particle">🌟</span>
                        <span class="particle">💫</span>
                    </div>
                </div>
                <div class="popup-text">答对了！</div>
                <div class="popup-points" id="earnedPoints">+10分</div>
            </div>
        </div>

        <!-- 错误提示 -->
        <div id="errorPopup" class="popup hidden">
            <div class="popup-content error">
                <div class="popup-icon shake">💪</div>
                <div class="popup-text">再试试看！</div>
                <div class="popup-encouragement" id="encouragementText">你很棒，继续努力！</div>
            </div>
        </div>

        <!-- 提示弹窗 -->
        <div id="hintPopup" class="popup hidden">
            <div class="popup-content hint">
                <div class="popup-icon">💡</div>
                <div class="popup-text">小提示</div>
                <div class="hint-content" id="hintContent">
                    <!-- 提示内容 -->
                </div>
                <button class="hint-close-btn" onclick="closeHint()">知道了</button>
            </div>
        </div>

        <!-- 加载动画 -->
        <div id="loadingOverlay" class="loading-overlay hidden">
            <div class="loading-content">
                <div class="loading-spinner">
                    <div class="spinner-ring"></div>
                    <div class="spinner-ring"></div>
                    <div class="spinner-ring"></div>
                </div>
                <div class="loading-text">正在加载...</div>
            </div>
        </div>

        <!-- 设置面板 -->
        <div id="settingsPanel" class="settings-panel hidden">
            <div class="settings-content">
                <div class="settings-header">
                    <h3>⚙️ 游戏设置</h3>
                    <button class="close-settings" onclick="closeSettings()">✕</button>
                </div>
                <div class="settings-options">
                    <div class="setting-item">
                        <label for="soundToggle">🔊 音效</label>
                        <input type="checkbox" id="soundToggle" checked>
                    </div>
                    <div class="setting-item">
                        <label for="musicToggle">🎵 背景音乐</label>
                        <input type="checkbox" id="musicToggle" checked>
                    </div>
                    <div class="setting-item">
                        <label for="difficultySelect">🎯 难度</label>
                        <select id="difficultySelect">
                            <option value="easy">简单</option>
                            <option value="normal" selected>普通</option>
                            <option value="hard">困难</option>
                        </select>
                    </div>
                    <div class="setting-item">
                        <label for="timeLimit">⏱️ 答题时间限制</label>
                        <select id="timeLimit">
                            <option value="30">30秒</option>
                            <option value="60" selected>60秒</option>
                            <option value="unlimited">无限制</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- 设置按钮 -->
        <button class="settings-btn" onclick="openSettings()">⚙️</button>
    </div>

    <script src="script-simple.js"></script>
</body>
</html>

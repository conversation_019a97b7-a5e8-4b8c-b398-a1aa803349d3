// 基础游戏数据
const gameData = {
    pictureWords: [
        { word: '苹果', pinyin: 'píng guǒ', meaning: '红色的水果', emoji: '🍎' },
        { word: '香蕉', pinyin: 'xiāng jiāo', meaning: '黄色弯弯的水果', emoji: '🍌' },
        { word: '小猫', pinyin: 'xiǎo māo', meaning: '毛茸茸的宠物', emoji: '🐱' },
        { word: '太阳', pinyin: 'tài yáng', meaning: '天空中发光发热', emoji: '☀️' },
        { word: '月亮', pinyin: 'yuè liang', meaning: '夜晚天空中的明月', emoji: '🌙' },
        { word: '花朵', pinyin: 'huā duǒ', meaning: '美丽的植物开的花', emoji: '🌸' },
        { word: '小鸟', pinyin: 'xiǎo niǎo', meaning: '会飞的小动物', emoji: '🐦' },
        { word: '汽车', pinyin: 'qì chē', meaning: '在路上跑的交通工具', emoji: '🚗' },
        { word: '房子', pinyin: 'fáng zi', meaning: '人们居住的地方', emoji: '🏠' },
        { word: '书本', pinyin: 'shū běn', meaning: '用来学习的读物', emoji: '📚' }
    ],
    pinyinWords: [
        { word: '妈妈', pinyin: 'mā ma', meaning: '最亲爱的人' },
        { word: '爸爸', pinyin: 'bà ba', meaning: '家里的男主人' },
        { word: '老师', pinyin: 'lǎo shī', meaning: '教我们知识的人' },
        { word: '朋友', pinyin: 'péng yǒu', meaning: '一起玩耍的伙伴' },
        { word: '学校', pinyin: 'xué xiào', meaning: '我们学习的地方' },
        { word: '家庭', pinyin: 'jiā tíng', meaning: '爸爸妈妈和我' },
        { word: '快乐', pinyin: 'kuài lè', meaning: '开心高兴的感觉' },
        { word: '美丽', pinyin: 'měi lì', meaning: '很好看很漂亮' },
        { word: '学习', pinyin: 'xué xí', meaning: '获得知识的过程' },
        { word: '春天', pinyin: 'chūn tiān', meaning: '温暖的季节' },
        { word: '晚安', pinyin: 'wǎn ān', meaning: '睡前的问候' },
        { word: '早晨', pinyin: 'zǎo chén', meaning: '一天的开始' }
    ],
    memoryPairs: [
        { word: '大', meaning: '体积很大' },
        { word: '小', meaning: '体积很小' },
        { word: '高', meaning: '距离地面远' },
        { word: '矮', meaning: '距离地面近' },
        { word: '快', meaning: '速度很快' },
        { word: '慢', meaning: '速度很慢' }
    ],
    encouragementMessages: [
        "你很棒，继续努力！",
        "加油，你一定可以的！",
        "再试试看，相信自己！",
        "很好，继续保持！",
        "太聪明了！",
        "答得真好！"
    ]
};

// 游戏状态
const gameState = {
    currentGame: null,
    currentQuestion: 0,
    totalQuestions: 0,
    score: 0,
    correctCount: 0,
    selectedAnswer: -1,
    startTime: 0,
    questionStartTime: 0,
    gameQuestions: [],
    soundEnabled: true,
    hintsUsed: 0,
    progress: {
        picture: { completed: 0, total: 0 },
        pinyin: { completed: 0, total: 0 },
        memory: { completed: 0, total: 0 },
        challenge: { completed: 0, total: 0 }
    }
};

// 当文档加载完成时初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('页面加载完成，初始化游戏...');
    showMenu();
    updateScore();
    updateProgressDisplay();
});

// 显示主菜单
function showMenu() {
    console.log('显示主菜单...');
    hideAllGameAreas();
    const gameMenu = document.getElementById('gameMenu');
    if (gameMenu) {
        gameMenu.classList.remove('hidden');
    }
    updateScore();
    updateProgressDisplay();
}

// 隐藏所有游戏区域
function hideAllGameAreas() {
    const gameAreas = ['gameMenu', 'pictureGame', 'pinyinGame', 'memoryGame', 'challengeGame', 'gameResult'];
    gameAreas.forEach(area => {
        const element = document.getElementById(area);
        if (element) {
            element.classList.add('hidden');
        }
    });
}

// 开始游戏
function startGame(gameType) {
    console.log('开始游戏:', gameType);
    
    gameState.currentGame = gameType;
    gameState.currentQuestion = 0;
    gameState.selectedAnswer = -1;
    gameState.startTime = Date.now();
    gameState.correctCount = 0;
    gameState.hintsUsed = 0;
    
    hideAllGameAreas();
    
    switch(gameType) {
        case 'picture':
            initPictureGame();
            break;
        case 'pinyin':
            initPinyinGame();
            break;
        case 'memory':
            initMemoryGame();
            break;
        case 'challenge':
            initChallengeGame();
            break;
        default:
            console.error('未知的游戏类型:', gameType);
            showMenu();
    }
}

// 初始化看图识字游戏
function initPictureGame() {
    console.log('初始化看图识字游戏...');
    const pictureGame = document.getElementById('pictureGame');
    if (pictureGame) {
        pictureGame.classList.remove('hidden');
        gameState.gameQuestions = shuffleArray([...gameData.pictureWords]).slice(0, 10);
        gameState.currentQuestion = 0;
        gameState.totalQuestions = gameState.gameQuestions.length;
        showPictureQuestion();
    } else {
        console.error('找不到看图识字游戏区域元素');
        alert('游戏初始化失败，请刷新页面重试');
        showMenu();
    }
}

// 显示看图识字题目
function showPictureQuestion() {
    console.log('显示第', gameState.currentQuestion + 1, '题');
    
    if (gameState.currentQuestion >= gameState.gameQuestions.length) {
        completeGame();
        return;
    }
    
    const question = gameState.gameQuestions[gameState.currentQuestion];
    const wrongAnswers = gameData.pictureWords
        .filter(item => item.word !== question.word)
        .sort(() => Math.random() - 0.5)
        .slice(0, 2);
    
    const answers = shuffleArray([question, ...wrongAnswers]);
    
    // 更新题目编号
    const currentQuestionElement = document.getElementById('currentQuestion');
    if (currentQuestionElement) {
        currentQuestionElement.textContent = gameState.currentQuestion + 1;
    }
    
    // 更新图片 - 使用emoji代替图片
    const questionImage = document.getElementById('questionImage');
    if (questionImage) {
        questionImage.style.display = 'none';
        let emojiContainer = document.querySelector('.emoji-container');
        if (!emojiContainer) {
            emojiContainer = document.createElement('div');
            emojiContainer.className = 'emoji-container';
            emojiContainer.style.cssText = `
                font-size: 120px;
                text-align: center;
                padding: 40px;
                background: linear-gradient(135deg, #fff, #f0f8ff);
                border-radius: 20px;
                box-shadow: 0 8px 32px rgba(0,0,0,0.1);
                margin: 20px 0;
                cursor: pointer;
                transition: transform 0.3s ease;
            `;
            questionImage.parentNode.appendChild(emojiContainer);
        }
        emojiContainer.textContent = question.emoji;
        emojiContainer.title = `点击听发音：${question.word}`;
        emojiContainer.onclick = () => speakWord(question.word, question.pinyin);
    }
    
    // 更新答案按钮
    const answerButtons = document.querySelectorAll('#pictureGame .answer-btn');
    answerButtons.forEach((btn, index) => {
        if (answers[index]) {
            const answerText = btn.querySelector('.answer-text');
            const answerPinyin = btn.querySelector('.answer-pinyin');
            
            if (answerText) answerText.textContent = answers[index].word;
            if (answerPinyin) answerPinyin.textContent = answers[index].pinyin;
            
            btn.className = 'answer-btn';
            btn.disabled = false;
            btn.dataset.correct = answers[index].word === question.word;
        }
    });
    
    // 隐藏下一题按钮，显示提示按钮
    const nextBtn = document.getElementById('nextBtn');
    const hintBtn = document.getElementById('hintBtn');
    if (nextBtn) nextBtn.classList.add('hidden');
    if (hintBtn) hintBtn.classList.remove('hidden');
    
    gameState.selectedAnswer = -1;
    gameState.questionStartTime = Date.now();
}

// 选择答案
function selectAnswer(index) {
    console.log('选择答案:', index);
    
    if (gameState.selectedAnswer !== -1) {
        console.log('已经选择过答案，忽略');
        return;
    }
    
    gameState.selectedAnswer = index;
    const buttons = document.querySelectorAll('#pictureGame .answer-btn');
    const selectedBtn = buttons[index];
    
    if (!selectedBtn) {
        console.error('找不到选中的按钮');
        return;
    }
    
    const isCorrect = selectedBtn.dataset.correct === 'true';
    console.log('答案是否正确:', isCorrect);
    
    if (isCorrect) {
        selectedBtn.classList.add('correct');
        const points = 10;
        gameState.score += points;
        gameState.correctCount++;
        updateScore();
        console.log(`+${points}分 太棒了！`);
        
        // 禁用所有按钮
        buttons.forEach(btn => btn.disabled = true);
        
        // 隐藏提示按钮
        const hintBtn = document.getElementById('hintBtn');
        if (hintBtn) hintBtn.classList.add('hidden');
        
        // 答对了自动进入下一题
        setTimeout(() => {
            nextQuestion();
        }, 1000);
    } else {
        selectedBtn.classList.add('incorrect');
        
        // 显示正确答案
        buttons.forEach(btn => {
            if (btn.dataset.correct === 'true') {
                btn.classList.add('correct');
            }
        });
        
        // 禁用所有按钮
        buttons.forEach(btn => btn.disabled = true);
        
        // 隐藏提示按钮
        const hintBtn = document.getElementById('hintBtn');
        if (hintBtn) hintBtn.classList.add('hidden');
        
        // 答错了显示下一题按钮
        setTimeout(() => {
            const nextBtn = document.getElementById('nextBtn');
            if (nextBtn) nextBtn.classList.remove('hidden');
        }, 1500);
    }
}

// 下一题
function nextQuestion() {
    console.log('进入下一题');
    gameState.currentQuestion++;
    showPictureQuestion();
}

// 提示功能
function showHint() {
    if (gameState.hintsUsed >= 3) {
        alert("提示次数已用完！");
        return;
    }
    
    const question = gameState.gameQuestions[gameState.currentQuestion];
    const hintText = `这个字的拼音是：${question.pinyin}\n含义是：${question.meaning}`;
    
    alert(hintText);
    gameState.hintsUsed++;
    gameState.score = Math.max(0, gameState.score - 2);
    updateScore();
}

// 完成游戏
function completeGame() {
    console.log('游戏完成');
    const accuracy = gameState.totalQuestions > 0 ? 
        Math.round((gameState.correctCount / gameState.totalQuestions) * 100) : 100;
    
    // 更新当前游戏模式的进度
    if (gameState.currentGame && gameState.progress[gameState.currentGame]) {
        gameState.progress[gameState.currentGame].completed = gameState.correctCount;
        gameState.progress[gameState.currentGame].total = gameState.totalQuestions;
    }
    
    let performanceMessage = '';
    let bonusPoints = 0;
    
    if (accuracy >= 90) {
        performanceMessage = '🏆 太棒了！你是汉字小能手！';
        bonusPoints = 20;
    } else if (accuracy >= 80) {
        performanceMessage = '🥇 很好！继续保持！';
        bonusPoints = 15;
    } else if (accuracy >= 70) {
        performanceMessage = '🥈 不错！还可以更好！';
        bonusPoints = 10;
    } else {
        performanceMessage = '🥉 加油！多练习就会进步！';
        bonusPoints = 5;
    }
    
    gameState.score += bonusPoints;
    updateScore();
    
    alert(`${performanceMessage}\n\n📊 游戏统计：\n得分：${gameState.score}分\n正确率：${accuracy}%\n答对题目：${gameState.correctCount}/${gameState.totalQuestions}\n奖励积分：+${bonusPoints}分`);
    showMenu();
}

// 语音播放功能
function speakText(text) {
    if (!gameState.soundEnabled || !window.speechSynthesis) {
        console.log('语音功能不可用或已禁用');
        return;
    }
    
    try {
        window.speechSynthesis.cancel();
        
        const utterance = new SpeechSynthesisUtterance(text);
        utterance.lang = 'zh-CN';
        utterance.rate = 0.7;
        utterance.pitch = 1.0;
        utterance.volume = 1;
        
        const voices = window.speechSynthesis.getVoices();
        const chineseVoice = voices.find(voice => 
            voice.lang.includes('zh') || voice.lang.includes('CN') || 
            voice.name.includes('Chinese') || voice.name.includes('中文')
        );
        
        if (chineseVoice) {
            utterance.voice = chineseVoice;
        }
        
        window.speechSynthesis.speak(utterance);
        console.log('播放语音:', text);
    } catch (error) {
        console.error('语音播放错误:', error);
    }
}

// 播放单词发音
function speakWord(word, pinyin) {
    speakText(word);
    console.log(`播放发音: ${word} (${pinyin})`);
}

// 更新分数显示
function updateScore() {
    const scoreElement = document.getElementById('score');
    if (scoreElement) {
        scoreElement.textContent = gameState.score;
    }
}

// 更新进度显示
function updateProgressDisplay() {
    // 看图识字进度
    const pictureProgress = document.getElementById('pictureProgress');
    if (pictureProgress) {
        if (gameState.progress.picture.total > 0) {
            const picturePercent = Math.round((gameState.progress.picture.completed / gameState.progress.picture.total) * 100);
            pictureProgress.style.width = `${picturePercent}%`;
        } else {
            pictureProgress.style.width = '0%';
        }
    }
    
    // 拼音识字进度
    const pinyinProgress = document.getElementById('pinyinProgress');
    if (pinyinProgress) {
        if (gameState.progress.pinyin.total > 0) {
            const pinyinPercent = Math.round((gameState.progress.pinyin.completed / gameState.progress.pinyin.total) * 100);
            pinyinProgress.style.width = `${pinyinPercent}%`;
        } else {
            pinyinProgress.style.width = '0%';
        }
    }
    
    // 记忆配对进度
    const memoryProgress = document.getElementById('memoryProgress');
    if (memoryProgress) {
        if (gameState.progress.memory.total > 0) {
            const memoryPercent = Math.round((gameState.progress.memory.completed / gameState.progress.memory.total) * 100);
            memoryProgress.style.width = `${memoryPercent}%`;
        } else {
            memoryProgress.style.width = '0%';
        }
    }
}

// 工具函数：打乱数组
function shuffleArray(array) {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
}

// 初始化拼音游戏
function initPinyinGame() {
    console.log('初始化拼音游戏...');
    const pinyinGame = document.getElementById('pinyinGame');
    if (pinyinGame) {
        pinyinGame.classList.remove('hidden');
        gameState.gameQuestions = shuffleArray([...gameData.pinyinWords]).slice(0, 10);
        gameState.currentQuestion = 0;
        gameState.totalQuestions = gameState.gameQuestions.length;
        showPinyinQuestion();
    } else {
        console.error('找不到拼音游戏区域元素');
        alert('游戏初始化失败，请刷新页面重试');
        showMenu();
    }
}

// 显示拼音题目
function showPinyinQuestion() {
    console.log('显示拼音题目第', gameState.currentQuestion + 1, '题');
    
    if (gameState.currentQuestion >= gameState.gameQuestions.length) {
        completeGame();
        return;
    }
    
    const question = gameState.gameQuestions[gameState.currentQuestion];
    const wrongAnswers = gameData.pinyinWords
        .filter(item => item.word !== question.word)
        .sort(() => Math.random() - 0.5)
        .slice(0, 2);
    
    const answers = shuffleArray([question, ...wrongAnswers]);
    
    // 更新题目编号
    const currentQuestionElement = document.getElementById('pinyinCurrentQuestion');
    if (currentQuestionElement) {
        currentQuestionElement.textContent = gameState.currentQuestion + 1;
    }
    
    // 更新拼音显示
    const pinyinText = document.getElementById('pinyinText');
    if (pinyinText) {
        pinyinText.textContent = question.pinyin;
    }
    
    // 更新答案按钮
    const answerButtons = document.querySelectorAll('#pinyinGame .answer-btn');
    answerButtons.forEach((btn, index) => {
        if (answers[index]) {
            const answerChar = btn.querySelector('.answer-char');
            const answerMeaning = btn.querySelector('.answer-meaning');
            
            if (answerChar) answerChar.textContent = answers[index].word;
            if (answerMeaning) answerMeaning.textContent = answers[index].meaning;
            
            btn.className = 'answer-btn large-text';
            btn.disabled = false;
            btn.dataset.correct = answers[index].word === question.word;
        }
    });
    
    // 隐藏下一题按钮，显示提示按钮
    const nextBtn = document.getElementById('pinyinNextBtn');
    const hintBtn = document.getElementById('pinyinHintBtn');
    if (nextBtn) nextBtn.classList.add('hidden');
    if (hintBtn) hintBtn.classList.remove('hidden');
    
    gameState.selectedAnswer = -1;
    gameState.questionStartTime = Date.now();
    
    // 自动播放汉字发音
    setTimeout(() => speakText(question.word), 500);
}

// 选择拼音答案
function selectPinyinAnswer(index) {
    console.log('选择拼音答案:', index);
    
    if (gameState.selectedAnswer !== -1) {
        return;
    }
    
    gameState.selectedAnswer = index;
    const buttons = document.querySelectorAll('#pinyinGame .answer-btn');
    const selectedBtn = buttons[index];
    
    if (!selectedBtn) {
        return;
    }
    
    const isCorrect = selectedBtn.dataset.correct === 'true';
    
    if (isCorrect) {
        selectedBtn.classList.add('correct');
        const points = 15;
        gameState.score += points;
        gameState.correctCount++;
        updateScore();
        console.log(`+${points}分 拼音读得真准！`);
        
        buttons.forEach(btn => btn.disabled = true);
        
        const hintBtn = document.getElementById('pinyinHintBtn');
        if (hintBtn) hintBtn.classList.add('hidden');
        
        setTimeout(() => {
            nextPinyinQuestion();
        }, 1000);
    } else {
        selectedBtn.classList.add('incorrect');
        
        buttons.forEach(btn => {
            if (btn.dataset.correct === 'true') {
                btn.classList.add('correct');
            }
        });
        
        buttons.forEach(btn => btn.disabled = true);
        
        const hintBtn = document.getElementById('pinyinHintBtn');
        if (hintBtn) hintBtn.classList.add('hidden');
        
        setTimeout(() => {
            const nextBtn = document.getElementById('pinyinNextBtn');
            if (nextBtn) nextBtn.classList.remove('hidden');
        }, 1500);
    }
}

// 下一拼音题
function nextPinyinQuestion() {
    gameState.currentQuestion++;
    showPinyinQuestion();
}

// 播放拼音
function speakPinyin() {
    const question = gameState.gameQuestions[gameState.currentQuestion];
    if (question) {
        speakText(question.word);
    }
}

// 拼音提示
function showPinyinHint() {
    if (gameState.hintsUsed >= 3) {
        alert("提示次数已用完！");
        return;
    }
    
    const question = gameState.gameQuestions[gameState.currentQuestion];
    alert(`这个词的含义是：${question.meaning}`);
    gameState.hintsUsed++;
    gameState.score = Math.max(0, gameState.score - 3);
    updateScore();
}

// 初始化记忆游戏
function initMemoryGame() {
    alert('记忆游戏功能即将推出！');
    showMenu();
}

// 初始化挑战游戏
function initChallengeGame() {
    alert('挑战游戏功能即将推出！');
    showMenu();
}

// 返回主菜单
function backToMenu() {
    console.log('返回主菜单');
    showMenu();
}

console.log('游戏JavaScript已加载完成');

// 增强的游戏数据
const gameData = {
    pictureWords: [
        { word: '苹果', pinyin: 'píng guǒ', meaning: '红色的水果', image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTIwIiByPSI2MCIgZmlsbD0iI2ZmNDQ0NCIvPjxlbGxpcHNlIGN4PSI4NSIgY3k9IjExMCIgcng9IjE1IiByeT0iMjAiIGZpbGw9IiNmZmFhYWEiLz48cGF0aCBkPSJNMTAwIDYwIEwxMDUgNDAgTDExMCA2MCIgc3Ryb2tlPSIjNjY4ODAwIiBzdHJva2Utd2lkdGg9IjQiIGZpbGw9Im5vbmUiLz48ZWxsaXBzZSBjeD0iMTEwIiBjeT0iNTAiIHJ4PSI4IiByeT0iMTIiIGZpbGw9IiM0NGFhNDQiLz48L3N2Zz4=' },
        { word: '香蕉', pinyin: 'xiāng jiāo', meaning: '黄色弯弯的水果', image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTgwIDQwIEMxMDAgMzAgMTIwIDQwIDEzMCA2MCBMMTI1IDE0MCBDMTI1IDE2MCA5NSAxNzAgODAgMTUwIEw4NSA3MCBDODUgNTUgODAgNDUgODAgNDAiIGZpbGw9IiNmZmRkMDAiLz48cGF0aCBkPSJNODAgNDAgTDEzMCA2MCBMMTI1IDE0MCBMODA2IDE1MCBMODUgNzAiIHN0cm9rZT0iI2VlYmIwMCIgc3Ryb2tlLXdpZHRoPSIyIiBmaWxsPSJub25lIi8+PC9zdmc+' },
        { word: '小猫', pinyin: 'xiǎo māo', meaning: '毛茸茸的宠物', image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTIwIiByPSI1MCIgZmlsbD0iI2ZmYWE2NiIvPjx0cmlhbmdsZSBjeDE9Ijc1IiBjeTE9Ijg1IiBjeDI9IjgwIiBjeTI9IjY1IiBjeDM9Ijg1IiBjeTM9Ijg1IiBmaWxsPSIjZmZhYTY2Ii8+PHRyaWFuZ2xlIGN4MT0iMTE1IiBjeTE9Ijg1IiBjeDI9IjEyMCIgY3kyPSI2NSIgY3gzPSIxMjUiIGN5Mz0iODUiIGZpbGw9IiNmZmFhNjYiLz48Y2lyY2xlIGN4PSI5MCIgY3k9IjExMCIgcj0iNSIgZmlsbD0iIzMzMzMzMyIvPjxjaXJjbGUgY3g9IjExMCIgY3k9IjExMCIgcj0iNSIgZmlsbD0iIzMzMzMzMyIvPjxwYXRoIGQ9Ik05NSAxMjUgTDEwNSAxMjUgTDEwMCAxMzUgWiIgZmlsbD0iIzMzMzMzMyIvPjxwYXRoIGQ9Ik04MCA5MCBMODA2IDEwMCBNMTIwIDkwIEwxMjAgMTAwIEE5NSAxMjUgTDEwMCAxMzUgWiIgZmlsbD0iIzMzMzMzMyIvPjwvc3ZnPg==' },
        { word: '小狗', pinyin: 'xiǎo gǒu', meaning: '忠诚的朋友', image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGVsbGlwc2UgY3g9IjEwMCIgY3k9IjEyMCIgcng9IjQ1IiByeT0iMzUiIGZpbGw9IiNhYTY2NDQiLz48ZWxsaXBzZSBjeD0iNzAiIGN5PSI5NSIgcng9IjE1IiByeT0iMjAiIGZpbGw9IiNhYTY2NDQiLz48ZWxsaXBzZSBjeD0iMTMwIiBjeT0iOTUiIHJ4PSIxNSIgcnk9IjIwIiBmaWxsPSIjYWE2NjQ0Ii8+PGNpcmNsZSBjeD0iODUiIGN5PSIxMTAiIHI9IjQiIGZpbGw9IiMzMzMzMzMiLz48Y2lyY2xlIGN4PSIxMTUiIGN5PSIxMTAiIHI9IjQiIGZpbGw9IiMzMzMzMzMiLz48ZWxsaXBzZSBjeD0iMTAwIiBjeT0iMTI1IiByeD0iOCIgcnk9IjUiIGZpbGw9IiMzMzMzMzMiLz48cGF0aCBkPSJNOTUgMTMwIEwxMDUgMTMwIiBzdHJva2U9IiMzMzMzMzMiIHN0cm9rZS13aWR0aD0iMiIvPjwvc3ZnPg==' },
        { word: '汽车', pinyin: 'qì chē', meaning: '交通工具', image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3QgeD0iNDAiIHk9IjEyMCIgd2lkdGg9IjEyMCIgaGVpZ2h0PSI0MCIgZmlsbD0iIzQ0ODhkZCIvPjxyZWN0IHg9IjcwIiB5PSI5MCIgd2lkdGg9IjYwIiBoZWlnaHQ9IjMwIiBmaWxsPSIjNjZhYWZmIi8+PGNpcmNsZSBjeD0iNzAiIGN5PSIxNzAiIHI9IjE1IiBmaWxsPSIjMzMzMzMzIi8+PGNpcmNsZSBjeD0iMTMwIiBjeT0iMTcwIiByPSIxNSIgZmlsbD0iIzMzMzMzMyIvPjxjaXJjbGUgY3g9IjcwIiBjeT0iMTcwIiByPSI4IiBmaWxsPSIjNjY2NjY2Ii8+PGNpcmNsZSBjeD0iMTMwIiBjeT0iMTcwIiByPSI4IiBmaWxsPSIjNjY2NjY2Ii8+PC9zdmc+' },
        { word: '房子', pinyin: 'fáng zi', meaning: '我们住的地方', image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBvbHlnb24gcG9pbnRzPSIxMDAsNDAgNTAsOTAgMTUwLDkwIiBmaWxsPSIjZGQ0NDQ0Ii8+PHJlY3QgeD0iNjAiIHk9IjkwIiB3aWR0aD0iODAiIGhlaWdodD0iODAiIGZpbGw9IiNmZmRkODgiLz48cmVjdCB4PSI5MCIgeT0iMTMwIiB3aWR0aD0iMjAiIGhlaWdodD0iNDAiIGZpbGw9IiM4ODQ0MDAiLz48cmVjdCB4PSI3MCIgeT0iMTEwIiB3aWR0aD0iMTUiIGhlaWdodD0iMTUiIGZpbGw9IiM2NmFhZmYiLz48cmVjdCB4PSIxMTUiIHk9IjExMCIgd2lkdGg9IjE1IiBoZWlnaHQ9IjE1IiBmaWxsPSIjNjZhYWZmIi8+PC9zdmc+' },
        { word: '太阳', pinyin: 'tài yáng', meaning: '天空中发光发热', image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSI0MCIgZmlsbD0iI2ZmZGQwMCIvPjxnIHRyYW5zZm9ybT0icm90YXRlKDAgMTAwIDEwMCkiPjxsaW5lIHgxPSIxMDAiIHkxPSIyMCIgeDI9IjEwMCIgeTI9IjQwIiBzdHJva2U9IiNmZmRkMDAiIHN0cm9rZS13aWR0aD0iNCIvPjwvZz48ZyB0cmFuc2Zvcm09InJvdGF0ZSg0NSAxMDAgMTAwKSI+PGxpbmUgeDE9IjEwMCIgeTE9IjIwIiB4Mj0iMTAwIiB5Mj0iNDAiIHN0cm9rZT0iI2ZmZGQwMCIgc3Ryb2tlLXdpZHRoPSI0Ii8+PC9nPjxnIHRyYW5zZm9ybT0icm90YXRlKDkwIDEwMCAxMDApIj48bGluZSB4MT0iMTAwIiB5MT0iMjAiIHgyPSIxMDAiIHkyPSI0MCIgc3Ryb2tlPSIjZmZkZDAwIiBzdHJva2Utd2lkdGg9IjQiLz48L2c+PGcgdHJhbnNmb3JtPSJyb3RhdGUoMTM1IDEwMCAxMDApIj48bGluZSB4MT0iMTAwIiB5MT0iMjAiIHgyPSIxMDAiIHkyPSI0MCIgc3Ryb2tlPSIjZmZkZDAwIiBzdHJva2Utd2lkdGg9IjQiLz48L2c+PGcgdHJhbnNmb3JtPSJyb3RhdGUoMTgwIDEwMCAxMDApIj48bGluZSB4MT0iMTAwIiB5MT0iMjAiIHgyPSIxMDAiIHkyPSI0MCIgc3Ryb2tlPSIjZmZkZDAwIiBzdHJva2Utd2lkdGg9IjQiLz48L2c+PGcgdHJhbnNmb3JtPSJyb3RhdGUoMjI1IDEwMCAxMDApIj48bGluZSB4MT0iMTAwIiB5MT0iMjAiIHgyPSIxMDAiIHkyPSI0MCIgc3Ryb2tlPSIjZmZkZDAwIiBzdHJva2Utd2lkdGg9IjQiLz48L2c+PGcgdHJhbnNmb3JtPSJyb3RhdGUoMjcwIDEwMCAxMDApIj48bGluZSB4MT0iMTAwIiB5MT0iMjAiIHgyPSIxMDAiIHkyPSI0MCIgc3Ryb2tlPSIjZmZkZDAwIiBzdHJva2Utd2lkdGg9IjQiLz48L2c+PGcgdHJhbnNmb3JtPSJyb3RhdGUoMzE1IDEwMCAxMDApIj48bGluZSB4MT0iMTAwIiB5MT0iMjAiIHgyPSIxMDAiIHkyPSI0MCIgc3Ryb2tlPSIjZmZkZDAwIiBzdHJva2Utd2lkdGg9IjQiLz48L2c+PC9zdmc+' },
        { word: '月亮', pinyin: 'yuè liàng', meaning: '夜晚天空的亮光', image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHBhdGggZD0iTTEyMCA1MCBDMTU1IDUwIDE3MCA4MCAxNzAgMTAwIEMxNzAgMTIwIDE1NSAxNTAgMTIwIDE1MCBDMTA1IDEzNSA5NSAxMTggOTUgMTAwIEM5NSA4MiAxMDUgNjUgMTIwIDUwIFoiIGZpbGw9IiNmZmZmODgiLz48Y2lyY2xlIGN4PSIxMzAiIGN5PSI4MCIgcj0iMyIgZmlsbD0iI2RkZGRkZCIvPjxjaXJjbGUgY3g9IjE0NSIgY3k9IjEwNSIgcj0iMiIgZmlsbD0iI2RkZGRkZCIvPjxjaXJjbGUgY3g9IjEzNSIgY3k9IjEyNSIgcj0iMiIgZmlsbD0iI2RkZGRkZCIvPjwvc3ZnPg==' },
        { word: '大树', pinyin: 'dà shù', meaning: '高大的植物', image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3QgeD0iOTAiIHk9IjEyMCIgd2lkdGg9IjIwIiBoZWlnaHQ9IjYwIiBmaWxsPSIjODg0NDAwIi8+PGNpcmNsZSBjeD0iMTAwIiBjeT0iODAiIHI9IjQwIiBmaWxsPSIjNDRhYTQ0Ii8+PGNpcmNsZSBjeD0iODAiIGN5PSI3MCIgcj0iMjUiIGZpbGw9IiM0NGFhNDQiLz48Y2lyY2xlIGN4PSIxMjAiIGN5PSI3MCIgcj0iMjUiIGZpbGw9IiM0NGFhNDQiLz48Y2lyY2xlIGN4PSIxMDAiIGN5PSI1MCIgcj0iMjAiIGZpbGw9IiM0NGFhNDQiLz48L3N2Zz4=' },
        { word: '小鸟', pinyin: 'xiǎo niǎo', meaning: '会飞的小动物', image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGVsbGlwc2UgY3g9IjEyMCIgY3k9IjEwMCIgcng9IjMwIiByeT0iMjAiIGZpbGw9IiNmZmRkNDQiLz48Y2lyY2xlIGN4PSIxMDAiIGN5PSI4NSIgcj0iMjAiIGZpbGw9IiNmZmRkNDQiLz48dHJpYW5nbGUgY3gxPSI3NSIgY3kxPSI4NSIgY3gyPSI2NSIgY3kyPSI5MCIgY3gzPSI3NSIgY3kzPSI5NSIgZmlsbD0iI2ZmYWE0NCIvPjxjaXJjbGUgY3g9IjEwNSIgY3k9IjgwIiByPSIzIiBmaWxsPSIjMzMzMzMzIi8+PGVsbGlwc2UgY3g9IjE0MCIgY3k9IjEwNSIgcng9IjE1IiByeT0iMjUiIGZpbGw9IiNmZmRkNDQiLz48cGF0aCBkPSJNMTQwIDc1IEwxNjAgNjUgTDE2MCA4NSBMMTUwIDkwIFoiIGZpbGw9IiNmZmRkNDQiLz48L3N2Zz4=' },
        { word: '花朵', pinyin: 'huā duǒ', meaning: '美丽的植物花', image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PGNpcmNsZSBjeD0iMTAwIiBjeT0iMTAwIiByPSIxNSIgZmlsbD0iI2ZmZGQwMCIvPjxjaXJjbGUgY3g9IjEwMCIgY3k9IjcwIiByPSIyMCIgZmlsbD0iI2ZmNjZkZCIvPjxjaXJjbGUgY3g9IjEzMCIgY3k9IjEwMCIgcj0iMjAiIGZpbGw9IiNmZjY2ZGQiLz48Y2lyY2xlIGN4PSIxMDAiIGN5PSIxMzAiIHI9IjIwIiBmaWxsPSIjZmY2NmRkIi8+PGNpcmNsZSBjeD0iNzAiIGN5PSIxMDAiIHI9IjIwIiBmaWxsPSIjZmY2NmRkIi8+PGNpcmNsZSBjeD0iMTE1IiBjeT0iODAiIHI9IjE1IiBmaWxsPSIjZmY2NmRkIi8+PGNpcmNsZSBjeD0iMTE1IiBjeT0iMTIwIiByPSIxNSIgZmlsbD0iI2ZmNjZkZCIvPjxjaXJjbGUgY3g9Ijg1IiBjeT0iMTIwIiByPSIxNSIgZmlsbD0iI2ZmNjZkZCIvPjxjaXJjbGUgY3g9Ijg1IiBjeT0iODAiIHI9IjE1IiBmaWxsPSIjZmY2NmRkIi8+PC9zdmc+' },
        { word: '蝴蝶', pinyin: 'hú dié', meaning: '美丽的昆虫', image: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+PHJlY3QgeD0iOTUiIHk9IjcwIiB3aWR0aD0iMTAiIGhlaWdodD0iNjAiIGZpbGw9IiMzMzMzMzMiLz48ZWxsaXBzZSBjeD0iNzAiIGN5PSI4NSIgcng9IjI1IiByeT0iMjAiIGZpbGw9IiNmZjY2ZGQiLz48ZWxsaXBzZSBjeD0iMTMwIiBjeT0iODUiIHJ4PSIyNSIgcnk9IjIwIiBmaWxsPSIjZmY2NmRkIi8+PGVsbGlwc2UgY3g9IjcwIiBjeT0iMTE1IiByeD0iMjAiIHJ5PSIxNSIgZmlsbD0iIzY2YWFmZiIvPjxlbGxpcHNlIGN4PSIxMzAiIGN5PSIxMTUiIHJ4PSIyMCIgcnk9IjE1IiBmaWxsPSIjNjZhYWZmIi8+PGNpcmNsZSBjeD0iNjAiIGN5PSI3NSIgcj0iNCIgZmlsbD0iI2ZmZGQwMCIvPjxjaXJjbGUgY3g9IjE0MCIgY3k9Ijc1IiByPSI0IiBmaWxsPSIjZmZkZDAwIi8+PC9zdmc+' }
    ],
    
    pinyinWords: [
        { word: '妈妈', pinyin: 'mā ma', meaning: '母亲' },
        { word: '爸爸', pinyin: 'bà ba', meaning: '父亲' },
        { word: '我', pinyin: 'wǒ', meaning: '自己' },
        { word: '你', pinyin: 'nǐ', meaning: '对方' },
        { word: '好', pinyin: 'hǎo', meaning: '不错' },
        { word: '水', pinyin: 'shuǐ', meaning: '液体' },
        { word: '火', pinyin: 'huǒ', meaning: '燃烧' },
        { word: '山', pinyin: 'shān', meaning: '高山' },
        { word: '天', pinyin: 'tiān', meaning: '天空' },
        { word: '人', pinyin: 'rén', meaning: '人类' },
        { word: '大', pinyin: 'dà', meaning: '很大' },
        { word: '小', pinyin: 'xiǎo', meaning: '很小' },
        { word: '多', pinyin: 'duō', meaning: '很多' },
        { word: '少', pinyin: 'shǎo', meaning: '很少' },
        { word: '来', pinyin: 'lái', meaning: '到来' }
    ],
    
    memoryPairs: [
        { char: '日', meaning: '太阳' },
        { char: '月', meaning: '月亮' },
        { char: '水', meaning: '水滴' },
        { char: '火', meaning: '火焰' },
        { char: '山', meaning: '高山' },
        { char: '人', meaning: '人物' },
        { char: '大', meaning: '很大' },
        { char: '小', meaning: '很小' }
    ],
    
    encouragementMessages: [
        "你很棒，继续努力！",
        "加油，你一定可以的！",
        "再试试看，相信自己！",
        "不要放弃，成功就在前方！",
        "每次尝试都是进步！",
        "你已经很努力了！"
    ],
    
    achievements: [
        { id: 'first_correct', name: '初次答对', condition: 'firstCorrect', icon: '🌟' },
        { id: 'speed_master', name: '速度达人', condition: 'fastAnswer', icon: '⚡' },
        { id: 'perfectionist', name: '完美主义者', condition: 'perfectScore', icon: '💯' },
        { id: 'persistent', name: '坚持不懈', condition: 'completeGame', icon: '🏆' },
        { id: 'memory_expert', name: '记忆专家', condition: 'memoryWin', icon: '🧠' },
        { id: 'pinyin_master', name: '拼音高手', condition: 'pinyinExpert', icon: '🎵' }
    ]
};

// 增强的游戏状态管理
let gameState = {
    currentGame: '',
    currentQuestion: 0,
    score: 0,
    gameQuestions: [],
    selectedAnswer: -1,
    memoryCards: [],
    flippedCards: [],
    matchedPairs: 0,
    startTime: null,
    questionStartTime: null,
    correctCount: 0,
    totalQuestions: 0,
    hintsUsed: 0,
    flipCount: 0,
    difficulty: 'normal',
    soundEnabled: true,
    musicEnabled: true,
    timeLimit: 60,
    achievements: [],
    gameStats: {
        picture: { completed: 0, total: 10 },
        pinyin: { completed: 0, total: 10 },
        memory: { completed: 0, total: 6 },
        challenge: { completed: 0, total: 15 }
    }
};

// 设置管理
let settings = {
    soundEnabled: true,
    musicEnabled: true,
    difficulty: 'normal',
    timeLimit: 60
};

// 成就系统
let unlockedAchievements = new Set();

// 时间管理
let gameTimer = null;
let questionTimer = null;

// 初始化游戏
document.addEventListener('DOMContentLoaded', function() {
    loadSettings();
    loadGameProgress();
    showMenu();
    initializeStars();
    updateLevelIndicator();
});

// 加载设置
function loadSettings() {
    const savedSettings = localStorage.getItem('hanziGameSettings');
    if (savedSettings) {
        const parsed = JSON.parse(savedSettings);
        Object.assign(settings, parsed);
        applySettings();
    }
}

// 应用设置
function applySettings() {
    document.getElementById('soundToggle').checked = settings.soundEnabled;
    document.getElementById('musicToggle').checked = settings.musicEnabled;
    document.getElementById('difficultySelect').value = settings.difficulty;
    document.getElementById('timeLimit').value = settings.timeLimit;
    
    gameState.soundEnabled = settings.soundEnabled;
    gameState.musicEnabled = settings.musicEnabled;
    gameState.difficulty = settings.difficulty;
    gameState.timeLimit = settings.timeLimit;
}

// 保存设置
function saveSettings() {
    localStorage.setItem('hanziGameSettings', JSON.stringify(settings));
}

// 加载游戏进度
function loadGameProgress() {
    const savedProgress = localStorage.getItem('hanziGameProgress');
    if (savedProgress) {
        const parsed = JSON.parse(savedProgress);
        Object.assign(gameState.gameStats, parsed);
        updateProgressDisplay();
    }
    
    const savedAchievements = localStorage.getItem('hanziGameAchievements');
    if (savedAchievements) {
        unlockedAchievements = new Set(JSON.parse(savedAchievements));
    }
}

// 保存游戏进度
function saveGameProgress() {
    localStorage.setItem('hanziGameProgress', JSON.stringify(gameState.gameStats));
    localStorage.setItem('hanziGameAchievements', JSON.stringify([...unlockedAchievements]));
}

// 更新进度显示
function updateProgressDisplay() {
    // 更新看图识字进度
    const pictureProgress = (gameState.gameStats.picture.completed / gameState.gameStats.picture.total) * 100;
    document.getElementById('pictureProgress').style.width = `${pictureProgress}%`;
    document.getElementById('pictureProgressText').textContent = 
        `${gameState.gameStats.picture.completed}/${gameState.gameStats.picture.total}`;
    
    // 更新拼音识字进度
    const pinyinProgress = (gameState.gameStats.pinyin.completed / gameState.gameStats.pinyin.total) * 100;
    document.getElementById('pinyinProgress').style.width = `${pinyinProgress}%`;
    document.getElementById('pinyinProgressText').textContent = 
        `${gameState.gameStats.pinyin.completed}/${gameState.gameStats.pinyin.total}`;
    
    // 更新记忆配对进度
    const memoryProgress = (gameState.gameStats.memory.completed / gameState.gameStats.memory.total) * 100;
    document.getElementById('memoryProgress').style.width = `${memoryProgress}%`;
    document.getElementById('memoryProgressText').textContent = 
        `${gameState.gameStats.memory.completed}/${gameState.gameStats.memory.total}`;
}

// 初始化星星评分系统
function initializeStars() {
    updateStars();
}

// 更新星星显示
function updateStars() {
    const stars = document.querySelectorAll('.star');
    let activeStars = 0;
    
    if (gameState.score >= 50) activeStars = 1;
    if (gameState.score >= 100) activeStars = 2;
    if (gameState.score >= 200) activeStars = 3;
    
    stars.forEach((star, index) => {
        if (index < activeStars) {
            star.classList.add('active');
        } else {
            star.classList.remove('active');
        }
    });
}

// 更新等级指示器
function updateLevelIndicator() {
    const levelText = document.getElementById('levelText');
    if (gameState.score < 50) {
        levelText.textContent = '新手';
    } else if (gameState.score < 100) {
        levelText.textContent = '学徒';
    } else if (gameState.score < 200) {
        levelText.textContent = '高手';
    } else if (gameState.score < 500) {
        levelText.textContent = '专家';
    } else {
        levelText.textContent = '大师';
    }
}

// 显示主菜单
function showMenu() {
    hideAllGameAreas();
    document.getElementById('gameMenu').classList.remove('hidden');
    updateScore();
    updateProgressDisplay();
    updateStars();
    updateLevelIndicator();
}

// 隐藏所有游戏区域
function hideAllGameAreas() {
    const gameAreas = ['gameMenu', 'pictureGame', 'pinyinGame', 'memoryGame', 'challengeGame', 'gameResult'];
    gameAreas.forEach(area => {
        const element = document.getElementById(area);
        if (element) {
            element.classList.add('hidden');
        }
    });
}

// 开始游戏
function startGame(gameType) {
    showLoadingOverlay();
    
    setTimeout(() => {
        gameState.currentGame = gameType;
        gameState.currentQuestion = 0;
        gameState.selectedAnswer = -1;
        gameState.startTime = Date.now();
        gameState.correctCount = 0;
        gameState.hintsUsed = 0;
        
        hideAllGameAreas();
        hideLoadingOverlay();
        
        switch(gameType) {
            case 'picture':
                initPictureGame();
                break;
            case 'pinyin':
                initPinyinGame();
                break;
            case 'memory':
                initMemoryGame();
                break;
            case 'challenge':
                initChallengeGame();
                break;
        }
    }, 1000);
}

// 显示/隐藏加载覆盖层
function showLoadingOverlay() {
    document.getElementById('loadingOverlay').classList.remove('hidden');
}

function hideLoadingOverlay() {
    document.getElementById('loadingOverlay').classList.add('hidden');
}

// 初始化看图识字游戏
function initPictureGame() {
    document.getElementById('pictureGame').classList.remove('hidden');
    gameState.gameQuestions = shuffleArray([...gameData.pictureWords]).slice(0, 10);
    gameState.currentQuestion = 0;
    gameState.totalQuestions = gameState.gameQuestions.length;
    startQuestionTimer();
    showPictureQuestion();
    
    // 自动播放图片对应的发音
    setTimeout(() => {
        const question = gameState.gameQuestions[gameState.currentQuestion];
        speakText(question.word);
    }, 500);
}

// 显示看图识字题目
function showPictureQuestion() {
    if (gameState.currentQuestion >= gameState.gameQuestions.length) {
        completeGame();
        return;
    }
    
    const question = gameState.gameQuestions[gameState.currentQuestion];
    const wrongAnswers = gameData.pictureWords
        .filter(item => item.word !== question.word)
        .sort(() => Math.random() - 0.5)
        .slice(0, 2);
    
    const answers = shuffleArray([question, ...wrongAnswers]);
    
    // 更新界面
    document.getElementById('currentQuestion').textContent = gameState.currentQuestion + 1;
    document.getElementById('questionImage').src = question.image;
    
    const answerButtons = document.querySelectorAll('#pictureGame .answer-btn');
    answerButtons.forEach((btn, index) => {
        const answerText = btn.querySelector('.answer-text');
        const answerPinyin = btn.querySelector('.answer-pinyin');
        
        answerText.textContent = answers[index].word;
        answerPinyin.textContent = answers[index].pinyin;
        
        btn.className = 'answer-btn';
        btn.disabled = false;
        btn.dataset.correct = answers[index].word === question.word;
    });
    
    document.getElementById('nextBtn').classList.add('hidden');
    document.getElementById('hintBtn').classList.remove('hidden');
    gameState.selectedAnswer = -1;
    
    resetQuestionTimer();
    startQuestionTimer();
}

// 显示图片提示
function showImageHint() {
    const hint = document.getElementById('imageHint');
    hint.style.opacity = '1';
    setTimeout(() => {
        hint.style.opacity = '0';
    }, 2000);
}

// 选择答案
function selectAnswer(index) {
    if (gameState.selectedAnswer !== -1) return;
    
    gameState.selectedAnswer = index;
    const buttons = document.querySelectorAll('#pictureGame .answer-btn');
    const selectedBtn = buttons[index];
    const isCorrect = selectedBtn.dataset.correct === 'true';
    
    clearQuestionTimer();
    
    if (isCorrect) {
        selectedBtn.classList.add('correct');
        const points = calculatePoints();
        gameState.score += points;
        gameState.correctCount++;
        updateScore();
        showPopup('success', `+${points}分`);
        playSuccessSound();
        checkAchievements('firstCorrect');
        
        // 检查速度成就
        const timeSpent = Date.now() - gameState.questionStartTime;
        if (timeSpent < 5000) {
            checkAchievements('fastAnswer');
        }
    } else {
        selectedBtn.classList.add('incorrect');
        const encouragement = gameData.encouragementMessages[
            Math.floor(Math.random() * gameData.encouragementMessages.length)
        ];
        showPopup('error', encouragement);
        playErrorSound();
        
        // 显示正确答案
        buttons.forEach(btn => {
            if (btn.dataset.correct === 'true') {
                btn.classList.add('correct');
            }
        });
    }
    
    // 禁用所有按钮
    buttons.forEach(btn => btn.disabled = true);
    document.getElementById('hintBtn').classList.add('hidden');
    
    // 显示下一题按钮
    setTimeout(() => {
        document.getElementById('nextBtn').classList.remove('hidden');
    }, 1500);
}

// 下一题
function nextQuestion() {
    currentQuestion++;
    showPictureQuestion();
}

// 初始化拼音识字游戏
function initPinyinGame() {
    document.getElementById('pinyinGame').classList.remove('hidden');
    gameQuestions = shuffleArray([...gameData.pinyinWords]).slice(0, 10);
    currentQuestion = 0;
    showPinyinQuestion();
}

// 显示拼音识字题目
function showPinyinQuestion() {
    if (currentQuestion >= gameQuestions.length) {
        showGameResult();
        return;
    }
    
    const question = gameQuestions[currentQuestion];
    const wrongAnswers = gameData.pinyinWords
        .filter(item => item.word !== question.word)
        .sort(() => Math.random() - 0.5)
        .slice(0, 2);
    
    const answers = shuffleArray([question, ...wrongAnswers]);
    
    // 更新界面
    document.getElementById('pinyinCurrentQuestion').textContent = currentQuestion + 1;
    document.getElementById('pinyinText').textContent = question.pinyin;
    
    const answerButtons = document.querySelectorAll('#pinyinGame .answer-btn');
    answerButtons.forEach((btn, index) => {
        btn.textContent = answers[index].word;
        btn.className = 'answer-btn large-text';
        btn.disabled = false;
        btn.dataset.correct = answers[index].word === question.word;
    });
    
    document.getElementById('pinyinNextBtn').classList.add('hidden');
    selectedAnswer = -1;
}

// 选择拼音答案
function selectPinyinAnswer(index) {
    if (selectedAnswer !== -1) return;
    
    selectedAnswer = index;
    const buttons = document.querySelectorAll('#pinyinGame .answer-btn');
    const selectedBtn = buttons[index];
    const isCorrect = selectedBtn.dataset.correct === 'true';
    
    if (isCorrect) {
        selectedBtn.classList.add('correct');
        score += 10;
        updateScore();
        showPopup('success');
        playSuccessSound();
    } else {
        selectedBtn.classList.add('incorrect');
        showPopup('error');
        playErrorSound();
        
        // 显示正确答案
        buttons.forEach(btn => {
            if (btn.dataset.correct === 'true') {
                btn.classList.add('correct');
            }
        });
    }
    
    // 禁用所有按钮
    buttons.forEach(btn => btn.disabled = true);
    
    // 显示下一题按钮
    setTimeout(() => {
        document.getElementById('pinyinNextBtn').classList.remove('hidden');
    }, 1500);
}

// 下一题（拼音游戏）
function nextPinyinQuestion() {
    currentQuestion++;
    showPinyinQuestion();
}

// 朗读拼音
function speakPinyin() {
    const pinyinText = document.getElementById('pinyinText').textContent;
    if ('speechSynthesis' in window) {
        const utterance = new SpeechSynthesisUtterance(pinyinText);
        utterance.lang = 'zh-CN';
        utterance.rate = 0.8;
        speechSynthesis.speak(utterance);
    }
}

// 初始化记忆配对游戏
function initMemoryGame() {
    document.getElementById('memoryGame').classList.remove('hidden');
    matchedPairs = 0;
    flippedCards = [];
    updateMemoryProgress();
    generateMemoryCards();
}

// 生成记忆卡片
function generateMemoryCards() {
    const grid = document.getElementById('memoryGrid');
    grid.innerHTML = '';
    
    memoryCards = [];
    gameData.memoryPairs.forEach(pair => {
        memoryCards.push({ type: 'char', content: pair.char, pair: pair.char });
        memoryCards.push({ type: 'meaning', content: pair.meaning, pair: pair.char });
    });
    
    memoryCards = shuffleArray(memoryCards);
    
    memoryCards.forEach((card, index) => {
        const cardElement = document.createElement('button');
        cardElement.className = 'memory-card';
        cardElement.dataset.index = index;
        cardElement.dataset.pair = card.pair;
        cardElement.dataset.content = card.content;
        cardElement.textContent = '?';
        cardElement.onclick = () => flipCard(index);
        grid.appendChild(cardElement);
    });
}

// 翻转卡片
function flipCard(index) {
    const card = document.querySelector(`[data-index="${index}"]`);
    
    if (card.classList.contains('flipped') || card.classList.contains('matched') || flippedCards.length >= 2) {
        return;
    }
    
    card.classList.add('flipped');
    card.textContent = memoryCards[index].content;
    flippedCards.push(index);
    
    if (flippedCards.length === 2) {
        setTimeout(checkMatch, 1000);
    }
}

// 检查匹配
function checkMatch() {
    const card1Index = flippedCards[0];
    const card2Index = flippedCards[1];
    const card1 = document.querySelector(`[data-index="${card1Index}"]`);
    const card2 = document.querySelector(`[data-index="${card2Index}"]`);
    
    if (memoryCards[card1Index].pair === memoryCards[card2Index].pair) {
        // 匹配成功
        card1.classList.add('matched');
        card2.classList.add('matched');
        matchedPairs++;
        score += 20;
        updateScore();
        updateMemoryProgress();
        playSuccessSound();
        
        if (matchedPairs === gameData.memoryPairs.length) {
            setTimeout(showGameResult, 1000);
        }
    } else {
        // 匹配失败
        card1.classList.remove('flipped');
        card2.classList.remove('flipped');
        card1.textContent = '?';
        card2.textContent = '?';
        playErrorSound();
    }
    
    flippedCards = [];
}

// 更新记忆游戏进度
function updateMemoryProgress() {
    document.getElementById('foundPairs').textContent = matchedPairs;
}

// 重新开始记忆游戏
function restartMemoryGame() {
    matchedPairs = 0;
    flippedCards = [];
    updateMemoryProgress();
    generateMemoryCards();
}

// 显示游戏结果
function showGameResult() {
    hideAllGameAreas();
    document.getElementById('gameResult').classList.remove('hidden');
    document.getElementById('finalScore').textContent = score;
    
    let message = '';
    if (score >= 180) {
        message = '太棒了！你是汉字小天才！🌟';
    } else if (score >= 120) {
        message = '非常好！继续加油！👏';
    } else if (score >= 60) {
        message = '不错哦！多练习会更好！😊';
    } else {
        message = '继续努力！你一定可以的！💪';
    }
    
    document.getElementById('resultMessage').textContent = message;
}

// 返回主菜单
function backToMenu() {
    showMenu();
}

// 重新开始当前游戏
function restartCurrentGame() {
    score = 0;
    updateScore();
    startGame(currentGame);
}

// 更新分数
function updateScore() {
    document.getElementById('score').textContent = score;
}

// 显示弹窗
function showPopup(type) {
    const popup = document.getElementById(type + 'Popup');
    popup.classList.remove('hidden');
    
    setTimeout(() => {
        popup.classList.add('hidden');
    }, 1500);
}

// 播放成功音效
function playSuccessSound() {
    // 使用Web Audio API创建简单的成功音效
    if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {
        const audioContext = new (AudioContext || webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.setValueAtTime(523.25, audioContext.currentTime); // C5
        oscillator.frequency.setValueAtTime(659.25, audioContext.currentTime + 0.1); // E5
        oscillator.frequency.setValueAtTime(783.99, audioContext.currentTime + 0.2); // G5
        
        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.5);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.5);
    }
}

// 播放错误音效
function playErrorSound() {
    if (typeof AudioContext !== 'undefined' || typeof webkitAudioContext !== 'undefined') {
        const audioContext = new (AudioContext || webkitAudioContext)();
        const oscillator = audioContext.createOscillator();
        const gainNode = audioContext.createGain();
        
        oscillator.connect(gainNode);
        gainNode.connect(audioContext.destination);
        
        oscillator.frequency.setValueAtTime(200, audioContext.currentTime);
        
        gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
        gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + 0.3);
        
        oscillator.start(audioContext.currentTime);
        oscillator.stop(audioContext.currentTime + 0.3);
    }
}

// 工具函数：打乱数组
function shuffleArray(array) {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
}

// 显示加载动画
function showLoadingOverlay() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.classList.remove('hidden');
    }
}

// 隐藏加载动画
function hideLoadingOverlay() {
    const overlay = document.getElementById('loadingOverlay');
    if (overlay) {
        overlay.classList.add('hidden');
    }
}

// 初始化看图识字游戏
function initPictureGame() {
    document.getElementById('pictureGame').classList.remove('hidden');
    gameState.gameQuestions = shuffleArray([...gameData.pictureWords]).slice(0, 10);
    gameState.currentQuestion = 0;
    gameState.totalQuestions = gameState.gameQuestions.length;
    showPictureQuestion();
}

// 显示看图识字题目
function showPictureQuestion() {
    if (gameState.currentQuestion >= gameState.gameQuestions.length) {
        completeGame();
        return;
    }
    
    const question = gameState.gameQuestions[gameState.currentQuestion];
    const wrongAnswers = gameData.pictureWords
        .filter(item => item.word !== question.word)
        .sort(() => Math.random() - 0.5)
        .slice(0, 2);
    
    const answers = shuffleArray([question, ...wrongAnswers]);
    
    // 更新界面
    document.getElementById('currentQuestion').textContent = gameState.currentQuestion + 1;
    document.getElementById('pictureImage').src = question.image;
    
    const answerButtons = document.querySelectorAll('#pictureGame .answer-btn');
    answerButtons.forEach((btn, index) => {
        btn.textContent = answers[index].word;
        btn.className = 'answer-btn';
        btn.disabled = false;
        btn.dataset.correct = answers[index].word === question.word;
    });
    
    document.getElementById('nextBtn').classList.add('hidden');
    document.getElementById('hintBtn').classList.remove('hidden');
    gameState.selectedAnswer = -1;
    
    resetQuestionTimer();
    startQuestionTimer();
}

// 选择答案
function selectAnswer(index) {
    if (gameState.selectedAnswer !== -1) return;
    
    gameState.selectedAnswer = index;
    const buttons = document.querySelectorAll('#pictureGame .answer-btn');
    const selectedBtn = buttons[index];
    const isCorrect = selectedBtn.dataset.correct === 'true';
    
    clearQuestionTimer();
    
    if (isCorrect) {
        selectedBtn.classList.add('correct');
        const points = calculatePoints();
        gameState.score += points;
        gameState.correctCount++;
        updateScore();
        showPopup('success', `+${points}分`);
        playSuccessSound();
    } else {
        selectedBtn.classList.add('incorrect');
        const encouragement = gameData.encouragementMessages[
            Math.floor(Math.random() * gameData.encouragementMessages.length)
        ];
        showPopup('error', encouragement);
        playErrorSound();
        
        // 显示正确答案
        buttons.forEach(btn => {
            if (btn.dataset.correct === 'true') {
                btn.classList.add('correct');
            }
        });
    }
    
    // 禁用所有按钮
    buttons.forEach(btn => btn.disabled = true);
    document.getElementById('hintBtn').classList.add('hidden');
    
    // 显示下一题按钮
    setTimeout(() => {
        document.getElementById('nextBtn').classList.remove('hidden');
    }, 1500);
}

// 更新分数显示
function updateScore() {
    const scoreElement = document.getElementById('score');
    if (scoreElement) {
        scoreElement.textContent = gameState.score;
    }
}

// 更新星星评级
function updateStars() {
    const stars = document.querySelectorAll('.star');
    const level = Math.floor(gameState.score / 100);
    stars.forEach((star, index) => {
        if (index < level) {
            star.classList.add('active');
        } else {
            star.classList.remove('active');
        }
    });
}

// 初始化星星
function initializeStars() {
    updateStars();
}

// 更新等级指示器
function updateLevelIndicator() {
    const levelElement = document.querySelector('.level-indicator');
    if (levelElement) {
        const level = Math.floor(gameState.score / 100) + 1;
        levelElement.textContent = `等级 ${level}`;
    }
}

// 更新进度显示
function updateProgressDisplay() {
    // 简化版进度更新
    const progressItems = document.querySelectorAll('.progress-fill');
    progressItems.forEach((item, index) => {
        const progress = Math.min((gameState.score / 500) * 100, 100);
        item.style.width = `${progress}%`;
    });
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>快乐学汉字 - 简化版</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="top-decoration">
        <div class="cloud cloud-1">☁️</div>
        <div class="cloud cloud-2">☁️</div>
        <div class="cloud cloud-3">☁️</div>
    </div>

    <div class="container">
        <!-- 头部 -->
        <header class="header">
            <div class="header-content">
                <h1>🎮 快乐学汉字</h1>
                <p class="subtitle">让学习变得有趣简单</p>
                <div class="score-container">
                    <div class="score">
                        <span class="score-label">得分:</span>
                        <span id="score" class="score-value">0</span>
                    </div>
                    <div class="score-stars">
                        <span class="star">⭐</span>
                        <span class="star">⭐</span>
                        <span class="star">⭐</span>
                        <span class="star">⭐</span>
                        <span class="star">⭐</span>
                    </div>
                    <div class="level-indicator">等级 1</div>
                </div>
            </div>
        </header>

        <!-- 主内容 -->
        <main class="main-content">
            <!-- 游戏菜单 -->
            <div id="gameMenu" class="game-area game-menu">
                <div class="menu-header">
                    <h2>选择游戏模式</h2>
                    <p class="menu-description">选择你最喜欢的学习方式开始游戏吧！</p>
                </div>
                <div class="menu-buttons">
                    <button class="menu-btn picture-btn" onclick="startGame('picture')">
                        <div class="btn-icon">🖼️</div>
                        <div class="btn-title">看图识字</div>
                        <div class="btn-description">通过图片学习汉字</div>
                        <div class="btn-level">适合: 初学者</div>
                    </button>
                    <button class="menu-btn pinyin-btn" onclick="startGame('pinyin')">
                        <div class="btn-icon">🎵</div>
                        <div class="btn-title">拼音识字</div>
                        <div class="btn-description">听拼音选汉字</div>
                        <div class="btn-level">适合: 进阶者</div>
                    </button>
                    <button class="menu-btn memory-btn" onclick="startGame('memory')">
                        <div class="btn-icon">🧠</div>
                        <div class="btn-title">记忆配对</div>
                        <div class="btn-description">汉字与含义配对</div>
                        <div class="btn-level">适合: 挑战者</div>
                    </button>
                    <button class="menu-btn challenge-btn" onclick="startGame('challenge')">
                        <div class="btn-icon">🏆</div>
                        <div class="btn-title">综合挑战</div>
                        <div class="btn-description">混合题型挑战</div>
                        <div class="btn-level">适合: 高手</div>
                    </button>
                </div>
                
                <!-- 学习进度 -->
                <div class="progress-section">
                    <h3>📈 学习进度</h3>
                    <div class="progress-grid">
                        <div class="progress-item">
                            <div class="progress-icon">🖼️</div>
                            <div class="progress-info">
                                <div class="progress-title">看图识字</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 20%"></div>
                                </div>
                                <div class="progress-text">已完成 2/10</div>
                            </div>
                        </div>
                        <div class="progress-item">
                            <div class="progress-icon">🎵</div>
                            <div class="progress-info">
                                <div class="progress-title">拼音识字</div>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 0%"></div>
                                </div>
                                <div class="progress-text">已完成 0/10</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 看图识字游戏 -->
            <div id="pictureGame" class="game-area hidden">
                <div class="game-header">
                    <button class="back-btn" onclick="backToMenu()">← 返回</button>
                    <div class="game-title">
                        <h3>🖼️ 看图识字</h3>
                        <p class="game-subtitle">根据图片选择正确的汉字</p>
                    </div>
                    <div class="progress-info">
                        <div class="progress">题目 <span id="currentQuestion">1</span>/4</div>
                    </div>
                </div>

                <div class="picture-container">
                    <div class="picture-box">
                        <img id="pictureImage" src="" alt="学习图片">
                        <div class="image-hint">点击听发音</div>
                    </div>
                    <div class="question-text">
                        <span class="question-icon">🤔</span>
                        这个是什么？
                    </div>
                </div>

                <div class="answer-options">
                    <button class="answer-btn" onclick="selectAnswer(0)">
                        <span class="answer-text">答案1</span>
                    </button>
                    <button class="answer-btn" onclick="selectAnswer(1)">
                        <span class="answer-text">答案2</span>
                    </button>
                    <button class="answer-btn" onclick="selectAnswer(2)">
                        <span class="answer-text">答案3</span>
                    </button>
                </div>

                <div class="game-controls">
                    <button id="hintBtn" class="hint-btn" onclick="showHint()">💡 提示</button>
                    <button id="nextBtn" class="next-btn hidden" onclick="nextQuestion()">下一题 →</button>
                </div>
            </div>
        </main>
    </div>

    <script src="script-simple.js"></script>
</body>
</html>

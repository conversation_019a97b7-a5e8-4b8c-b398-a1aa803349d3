/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #ff6b6b;
    --secondary-color: #4ecdc4;
    --accent-color: #45b7d1;
    --success-color: #96ceb4;
    --warning-color: #feca57;
    --error-color: #ff9ff3;
    --text-dark: #2c3e50;
    --text-light: #ecf0f1;
    --background-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --card-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
    --border-radius: 20px;
    --transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

body {
    font-family: 'Noto Sans SC', '微软雅黑', 'Microsoft YaHei', Arial, sans-serif;
    background: var(--background-gradient);
    min-height: 100vh;
    color: var(--text-dark);
    overflow-x: hidden;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="stars" x="0" y="0" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="3" cy="3" r="0.5" fill="rgba(255,255,255,0.3)"/><circle cx="13" cy="7" r="0.3" fill="rgba(255,255,255,0.2)"/><circle cx="7" cy="15" r="0.4" fill="rgba(255,255,255,0.25)"/></pattern></defs><rect width="100" height="100" fill="url(%23stars)"/></svg>') repeat;
    pointer-events: none;
    z-index: -1;
    animation: twinkle 20s linear infinite;
}

@keyframes twinkle {
    0%, 100% { opacity: 0.5; }
    50% { opacity: 1; }
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
    position: relative;
}

/* 顶部装饰云朵 */
.top-decoration {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 150px;
    pointer-events: none;
    z-index: 1;
}

.cloud {
    position: absolute;
    font-size: 2em;
    opacity: 0.7;
    animation: float 6s ease-in-out infinite;
}

.cloud-1 {
    left: 10%;
    top: 20px;
    animation-delay: 0s;
}

.cloud-2 {
    right: 15%;
    top: 40px;
    animation-delay: 2s;
}

.cloud-3 {
    left: 50%;
    top: 10px;
    animation-delay: 4s;
}

@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

/* 头部样式增强 */
.header {
    text-align: center;
    background: rgba(255, 255, 255, 0.95);
    border-radius: var(--border-radius);
    padding: 30px;
    margin-bottom: 30px;
    box-shadow: var(--card-shadow);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    position: relative;
    z-index: 2;
}

.header-content h1 {
    font-size: 3em;
    color: var(--primary-color);
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    font-weight: 900;
    background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: rainbow 3s ease-in-out infinite;
}

@keyframes rainbow {
    0%, 100% { filter: hue-rotate(0deg); }
    50% { filter: hue-rotate(45deg); }
}

.subtitle {
    font-size: 1.2em;
    color: var(--text-dark);
    margin-bottom: 20px;
    opacity: 0.8;
}

.score-container {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 30px;
    flex-wrap: wrap;
}

.score {
    display: flex;
    align-items: center;
    gap: 15px;
    background: linear-gradient(135deg, var(--secondary-color), var(--accent-color));
    padding: 15px 25px;
    border-radius: 50px;
    color: white;
    font-weight: bold;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.score-label {
    font-size: 1.2em;
}

.score-value {
    font-size: 1.5em;
    font-weight: 900;
}

.score-stars {
    display: flex;
    gap: 5px;
}

.star {
    font-size: 1.2em;
    opacity: 0.3;
    transition: var(--transition);
}

.star.active {
    opacity: 1;
    animation: starGlow 0.5s ease;
}

@keyframes starGlow {
    0% { transform: scale(1); }
    50% { transform: scale(1.3); }
    100% { transform: scale(1); }
}

.level-indicator {
    background: linear-gradient(135deg, var(--warning-color), #ff9ff3);
    padding: 10px 20px;
    border-radius: 25px;
    color: white;
    font-weight: bold;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

/* 主内容区域 */
.main-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: var(--border-radius);
    padding: 40px;
    box-shadow: var(--card-shadow);
    backdrop-filter: blur(10px);
    min-height: 600px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    position: relative;
}

/* 游戏菜单增强 */
.game-menu {
    text-align: center;
}

.menu-header h2 {
    font-size: 2.5em;
    color: var(--text-dark);
    margin-bottom: 15px;
    font-weight: 700;
}

.menu-description {
    font-size: 1.3em;
    color: var(--text-dark);
    opacity: 0.7;
    margin-bottom: 40px;
}

.menu-buttons {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 30px;
    margin-bottom: 50px;
}

.menu-btn {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    border: none;
    border-radius: var(--border-radius);
    padding: 30px;
    color: white;
    cursor: pointer;
    transition: var(--transition);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    font-weight: 600;
    text-align: center;
    position: relative;
    overflow: hidden;
    min-height: 180px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

.menu-btn::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transform: rotate(45deg);
    transition: var(--transition);
    opacity: 0;
}

.menu-btn:hover::before {
    opacity: 1;
    animation: shine 0.6s ease;
}

@keyframes shine {
    0% { transform: translateX(-100%) translateY(-100%) rotate(45deg); }
    100% { transform: translateX(100%) translateY(100%) rotate(45deg); }
}

.menu-btn:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.25);
}

.menu-btn:active {
    transform: translateY(-4px) scale(1.01);
}

.menu-btn.picture-btn {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.menu-btn.pinyin-btn {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

.menu-btn.memory-btn {
    background: linear-gradient(135deg, #d299c2 0%, #fef9d7 100%);
}

.menu-btn.challenge-btn {
    background: linear-gradient(135deg, #89f7fe 0%, #66a6ff 100%);
    border: 2px solid #ffd700;
    position: relative;
}

.menu-btn.challenge-btn::after {
    content: '🔥';
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 1.5em;
    animation: bounce 2s ease-in-out infinite;
}

.btn-icon {
    font-size: 3em;
    margin-bottom: 10px;
}

.btn-title {
    font-size: 1.5em;
    font-weight: 700;
    margin-bottom: 8px;
}

.btn-description {
    font-size: 1em;
    opacity: 0.9;
    margin-bottom: 8px;
}

.btn-level {
    font-size: 0.9em;
    opacity: 0.8;
    background: rgba(255, 255, 255, 0.2);
    padding: 5px 15px;
    border-radius: 15px;
}

/* 学习进度部分 */
.progress-section {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    border-radius: var(--border-radius);
    padding: 30px;
    margin-top: 40px;
    color: white;
}

.progress-section h3 {
    text-align: center;
    font-size: 1.8em;
    margin-bottom: 25px;
    font-weight: 700;
}

.progress-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.progress-item {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.progress-icon {
    font-size: 2em;
    margin-bottom: 10px;
}

.progress-info {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.progress-title {
    font-weight: 600;
    font-size: 1.1em;
}

.progress-bar {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    height: 10px;
    overflow: hidden;
}

.progress-fill {
    background: linear-gradient(90deg, #96ceb4, #feca57);
    height: 100%;
    border-radius: 10px;
    transition: width 0.5s ease;
}

.progress-text {
    font-size: 0.9em;
    opacity: 0.9;
}

/* 游戏区域通用样式增强 */
.game-area {
    animation: slideInUp 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 20px;
    border-bottom: 3px solid #eee;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: -40px -40px 30px -40px;
    padding: 25px 40px;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    color: white;
}

.back-btn {
    background: rgba(255, 255, 255, 0.2);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 12px 20px;
    cursor: pointer;
    font-size: 1em;
    font-weight: 600;
    transition: var(--transition);
    backdrop-filter: blur(10px);
}

.back-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateX(-5px);
}

.game-title {
    text-align: center;
    flex: 1;
}

.game-title h3 {
    font-size: 2em;
    margin-bottom: 5px;
    font-weight: 700;
}

.game-subtitle {
    font-size: 1em;
    opacity: 0.9;
}

.progress-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 5px;
}

.progress {
    font-size: 1.2em;
    font-weight: bold;
    background: rgba(255, 255, 255, 0.2);
    padding: 8px 15px;
    border-radius: 20px;
    backdrop-filter: blur(10px);
}

.time-limit, .time-display {
    font-size: 1em;
    background: var(--warning-color);
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-weight: 600;
}

/* 看图识字增强样式 */
.picture-container {
    text-align: center;
    margin-bottom: 40px;
}

.picture-box {
    background: white;
    border-radius: var(--border-radius);
    padding: 30px;
    margin-bottom: 20px;
    box-shadow: var(--card-shadow);
    display: inline-block;
    position: relative;
    border: 3px solid #f0f0f0;
    transition: var(--transition);
    cursor: pointer;
}

.picture-box:hover {
    transform: scale(1.02);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
    border-color: var(--accent-color);
}

.picture-box img {
    max-width: 300px;
    max-height: 300px;
    border-radius: 15px;
    object-fit: cover;
    transition: var(--transition);
}

.image-hint {
    position: absolute;
    bottom: 10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--accent-color);
    color: white;
    padding: 5px 12px;
    border-radius: 15px;
    font-size: 0.9em;
    opacity: 0;
    transition: var(--transition);
}

.picture-box:hover .image-hint {
    opacity: 1;
}

.question-text {
    font-size: 1.8em;
    color: var(--text-dark);
    font-weight: bold;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 20px;
}

.question-icon {
    font-size: 1.2em;
    animation: bounce 2s ease-in-out infinite;
}

/* 拼音游戏增强样式 */
.pinyin-container {
    text-align: center;
    margin-bottom: 40px;
}

.pinyin-display {
    background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
    border-radius: var(--border-radius);
    padding: 40px;
    margin-bottom: 20px;
    display: inline-block;
    position: relative;
    box-shadow: var(--card-shadow);
    border: 3px solid rgba(255, 255, 255, 0.5);
}

.pinyin-text {
    font-size: 4em;
    color: var(--text-dark);
    font-weight: 900;
    margin-bottom: 15px;
    font-family: 'Courier New', monospace;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
    letter-spacing: 3px;
}

.speak-btn {
    background: var(--primary-color);
    border: none;
    border-radius: 50px;
    padding: 15px 25px;
    cursor: pointer;
    transition: var(--transition);
    color: white;
    font-weight: 600;
    display: flex;
    align-items: center;
    gap: 10px;
    margin: 0 auto;
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.3);
}

.speak-btn:hover {
    transform: scale(1.1);
    background: #ff5252;
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.4);
}

.speaker-icon {
    font-size: 1.5em;
}

.speaker-text {
    font-size: 1em;
}

.auto-play-indicator {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    background: var(--success-color);
    color: white;
    padding: 5px 15px;
    border-radius: 15px;
    font-size: 0.9em;
    opacity: 0;
    transition: var(--transition);
}

.auto-play-indicator.show {
    opacity: 1;
}

/* 答案选项增强样式 */
.answer-options {
    display: flex;
    justify-content: center;
    gap: 25px;
    margin-bottom: 40px;
    flex-wrap: wrap;
}

.answer-btn {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    border: none;
    border-radius: 18px;
    padding: 25px 35px;
    color: white;
    cursor: pointer;
    transition: var(--transition);
    min-width: 140px;
    font-weight: 700;
    box-shadow: 0 6px 20px rgba(79, 172, 254, 0.3);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
    border: 3px solid transparent;
}

.answer-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: var(--transition);
}

.answer-btn:hover::before {
    left: 100%;
}

.answer-btn:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow: 0 10px 30px rgba(79, 172, 254, 0.4);
    border-color: rgba(255, 255, 255, 0.5);
}

.answer-btn:active {
    transform: translateY(-2px) scale(1.01);
}

.answer-btn.correct {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    animation: correctPulse 0.8s ease;
    border-color: #4CAF50;
    box-shadow: 0 10px 30px rgba(86, 171, 47, 0.4);
}

.answer-btn.incorrect {
    background: linear-gradient(135deg, #ff416c 0%, #ff4b2b 100%);
    animation: shake 0.6s ease;
    border-color: #f44336;
    box-shadow: 0 10px 30px rgba(255, 65, 108, 0.4);
}

.answer-btn.large-text {
    font-size: 2.2em;
    min-width: 180px;
    padding: 30px 40px;
    min-height: 120px;
}

.answer-btn.large-text .answer-char {
    font-size: 1.2em;
    font-weight: 900;
}

.answer-btn.large-text .answer-meaning {
    font-size: 0.5em;
    opacity: 0.8;
    margin-top: 5px;
}

.answer-text {
    font-size: 1.4em;
    font-weight: 700;
}

.answer-pinyin {
    font-size: 0.9em;
    opacity: 0.9;
    font-style: italic;
}

/* 记忆游戏增强样式 */
.memory-stats {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 30px;
    flex-wrap: wrap;
}

.stat-item {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 15px 25px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
    font-weight: 600;
}

.stat-icon {
    font-size: 1.5em;
}

.stat-label {
    font-size: 1em;
}

.stat-value {
    font-size: 1.2em;
    font-weight: 700;
}

.memory-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
    max-width: 650px;
    margin: 0 auto 40px;
    padding: 20px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: var(--border-radius);
    backdrop-filter: blur(10px);
}

.memory-card {
    aspect-ratio: 1;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 18px;
    font-size: 1.8em;
    color: white;
    cursor: pointer;
    transition: var(--transition);
    position: relative;
    overflow: hidden;
    font-weight: 700;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.2);
    border: 3px solid transparent;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    min-height: 100px;
}

.memory-card::before {
    content: '';
    position: absolute;
    inset: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.2) 50%, transparent 70%);
    opacity: 0;
    transition: var(--transition);
}

.memory-card:hover::before {
    opacity: 1;
    animation: cardShine 0.6s ease;
}

@keyframes cardShine {
    0% { transform: translateX(-100%) skewX(-45deg); }
    100% { transform: translateX(200%) skewX(-45deg); }
}

.memory-card:hover {
    transform: scale(1.05) rotateY(5deg);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

.memory-card.flipped {
    background: white;
    color: var(--text-dark);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    border-color: var(--accent-color);
    transform: rotateY(180deg);
}

.memory-card.matched {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    color: white;
    cursor: default;
    animation: matchSuccess 0.8s ease;
    border-color: #4CAF50;
}

.memory-card.matched:hover {
    transform: none;
}

@keyframes matchSuccess {
    0% { transform: scale(1); }
    50% { transform: scale(1.1) rotateZ(5deg); }
    100% { transform: scale(1) rotateZ(0deg); }
}

/* 挑战模式样式 */
.challenge-type-indicator {
    text-align: center;
    margin-bottom: 30px;
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    padding: 15px 30px;
    border-radius: 25px;
    display: inline-flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
    font-weight: 600;
    color: var(--text-dark);
    margin-left: 50%;
    transform: translateX(-50%);
}

.type-icon {
    font-size: 1.8em;
}

.type-text {
    font-size: 1.2em;
}

.challenge-content {
    min-height: 400px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.challenge-score {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: var(--text-dark);
    padding: 8px 15px;
    border-radius: 20px;
    font-weight: 700;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
}

/* 游戏控制按钮增强 */
.game-controls {
    text-align: center;
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.next-btn, .restart-btn, .hint-btn {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    border: none;
    border-radius: 18px;
    padding: 18px 35px;
    font-size: 1.4em;
    color: white;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 700;
    box-shadow: 0 6px 20px rgba(255, 154, 158, 0.3);
    position: relative;
    overflow: hidden;
    display: flex;
    align-items: center;
    gap: 10px;
}

.hint-btn {
    background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
    box-shadow: 0 6px 20px rgba(254, 202, 87, 0.3);
}

.restart-btn {
    background: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    box-shadow: 0 6px 20px rgba(116, 185, 255, 0.3);
}

.next-btn:hover, .restart-btn:hover, .hint-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.25);
}

.next-btn:active, .restart-btn:active, .hint-btn:active {
    transform: translateY(-1px) scale(1.01);
}

/* 结果页面增强样式 */
.result-area {
    text-align: center;
    animation: resultAppear 0.8s cubic-bezier(0.25, 0.8, 0.25, 1);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: var(--border-radius);
    margin: -40px;
    padding: 40px;
}

@keyframes resultAppear {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.result-content {
    padding: 20px;
}

.result-animation {
    position: relative;
    margin-bottom: 30px;
}

.result-icon {
    font-size: 5em;
    margin-bottom: 20px;
    display: inline-block;
}
/* 弹窗增强样式 */
.popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    animation: popupAppear 0.6s cubic-bezier(0.25, 0.8, 0.25, 1);
}

@keyframes popupAppear {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.7);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.popup-content {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    color: white;
    padding: 40px;
    border-radius: var(--border-radius);
    text-align: center;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 3px solid rgba(255, 255, 255, 0.2);
    min-width: 280px;
    position: relative;
    overflow: hidden;
}

.popup-content::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: popupShine 2s linear infinite;
}

@keyframes popupShine {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.popup-content.error {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.popup-content.hint {
    background: linear-gradient(135deg, #feca57 0%, #ff9ff3 100%);
    max-width: 400px;
    text-align: left;
}

.popup-content.success {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
}

.popup-animation {
    position: relative;
    margin-bottom: 20px;
}

.popup-icon {
    font-size: 4em;
    margin-bottom: 20px;
    display: inline-block;
    position: relative;
    z-index: 1;
}

.popup-icon.bounce {
    animation: iconBounce 0.8s ease;
}

.popup-icon.shake {
    animation: iconShake 0.6s ease;
}

@keyframes iconBounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0) scale(1); }
    40% { transform: translateY(-20px) scale(1.1); }
    60% { transform: translateY(-10px) scale(1.05); }
}

@keyframes iconShake {
    0%, 100% { transform: translateX(0); }
    25% { transform: translateX(-10px) rotate(-5deg); }
    75% { transform: translateX(10px) rotate(5deg); }
}

.success-particles {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
}

.success-particles .particle {
    position: absolute;
    font-size: 1.5em;
    animation: successParticle 1s ease-out;
}

.success-particles .particle:nth-child(1) { left: -40px; animation-delay: 0s; }
.success-particles .particle:nth-child(2) { right: -40px; animation-delay: 0.2s; }
.success-particles .particle:nth-child(3) { top: -40px; animation-delay: 0.4s; }

@keyframes successParticle {
    0% { transform: scale(0) rotate(0deg); opacity: 0; }
    50% { transform: scale(1) rotate(180deg); opacity: 1; }
    100% { transform: scale(0) rotate(360deg); opacity: 0; }
}

.popup-text {
    font-size: 2em;
    font-weight: 700;
    margin-bottom: 15px;
    position: relative;
    z-index: 1;
}

.popup-points {
    font-size: 1.5em;
    font-weight: 700;
    color: #ffd700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    z-index: 1;
}

.popup-encouragement {
    font-size: 1.2em;
    margin-top: 10px;
    opacity: 0.9;
    position: relative;
    z-index: 1;
}

.hint-content {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 20px;
    margin: 20px 0;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    z-index: 1;
}

.hint-close-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 12px;
    padding: 10px 25px;
    color: white;
    cursor: pointer;
    font-weight: 600;
    transition: var(--transition);
    backdrop-filter: blur(10px);
    position: relative;
    z-index: 1;
}

.hint-close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-2px);
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    backdrop-filter: blur(5px);
}

.loading-content {
    text-align: center;
    color: white;
}

.loading-spinner {
    width: 80px;
    height: 80px;
    position: relative;
    margin: 0 auto 20px;
}

.spinner-ring {
    width: 100%;
    height: 100%;
    border: 4px solid transparent;
    border-top: 4px solid #fff;
    border-radius: 50%;
    position: absolute;
    animation: spin 1s linear infinite;
}

.spinner-ring:nth-child(2) {
    animation-delay: 0.3s;
    border-top-color: #ff6b6b;
}

.spinner-ring:nth-child(3) {
    animation-delay: 0.6s;
    border-top-color: #4ecdc4;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 1.5em;
    font-weight: 600;
}

/* 设置面板 */
.settings-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.95);
    border-radius: var(--border-radius);
    padding: 0;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    z-index: 1000;
    min-width: 400px;
    max-width: 90vw;
    animation: settingsAppear 0.5s ease;
}

@keyframes settingsAppear {
    from {
        opacity: 0;
        transform: translate(-50%, -50%) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.settings-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 30px;
    border-radius: var(--border-radius) var(--border-radius) 0 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.settings-header h3 {
    font-size: 1.5em;
    font-weight: 700;
}

.close-settings {
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 35px;
    height: 35px;
    color: white;
    cursor: pointer;
    font-size: 1.2em;
    font-weight: bold;
    transition: var(--transition);
}

.close-settings:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1);
}

.settings-options {
    padding: 30px;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.setting-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.setting-item label {
    font-size: 1.1em;
    font-weight: 600;
    color: var(--text-dark);
    display: flex;
    align-items: center;
    gap: 10px;
}

.setting-item input[type="checkbox"] {
    width: 50px;
    height: 25px;
    appearance: none;
    background: #ddd;
    border-radius: 25px;
    position: relative;
    cursor: pointer;
    transition: var(--transition);
}

.setting-item input[type="checkbox"]:checked {
    background: var(--accent-color);
}

.setting-item input[type="checkbox"]::before {
    content: '';
    position: absolute;
    top: 2px;
    left: 2px;
    width: 21px;
    height: 21px;
    background: white;
    border-radius: 50%;
    transition: var(--transition);
}

.setting-item input[type="checkbox"]:checked::before {
    transform: translateX(25px);
}

.setting-item select {
    background: white;
    border: 2px solid #ddd;
    border-radius: 10px;
    padding: 8px 15px;
    font-size: 1em;
    color: var(--text-dark);
    cursor: pointer;
    transition: var(--transition);
}

.setting-item select:focus {
    outline: none;
    border-color: var(--accent-color);
}

.settings-btn {
    position: fixed;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.9);
    border: none;
    border-radius: 50%;
    width: 50px;
    height: 50px;
    font-size: 1.5em;
    cursor: pointer;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
    transition: var(--transition);
    z-index: 100;
}

.settings-btn:hover {
    background: white;
    transform: scale(1.1) rotate(90deg);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}
@keyframes particleFloat {
    0%, 100% { 
        transform: translateY(0px) rotate(0deg);
        opacity: 0.7;
    }
    50% { 
        transform: translateY(-20px) rotate(180deg);
        opacity: 1;
    }
}

#resultTitle {
    font-size: 3em;
    margin-bottom: 30px;
    font-weight: 900;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.result-details {
    margin-bottom: 30px;
}

.result-score-big {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 20px;
    padding: 25px;
    margin-bottom: 25px;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
}

.score-label {
    display: block;
    font-size: 1.2em;
    margin-bottom: 10px;
    opacity: 0.9;
}

.score-number {
    font-size: 4em;
    font-weight: 900;
    color: #ffd700;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
}

.result-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 20px;
}

.stat-box {
    background: rgba(255, 255, 255, 0.15);
    border-radius: 15px;
    padding: 20px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: var(--transition);
}

.stat-box:hover {
    transform: translateY(-5px);
    background: rgba(255, 255, 255, 0.2);
}

.stat-box .stat-icon {
    font-size: 2.5em;
    margin-bottom: 10px;
    display: block;
}

.stat-box .stat-number {
    font-size: 1.8em;
    font-weight: 700;
    display: block;
    margin-bottom: 5px;
}

.stat-box .stat-label {
    font-size: 1em;
    opacity: 0.9;
}

.result-message {
    font-size: 1.8em;
    margin-bottom: 30px;
    font-weight: 600;
    opacity: 0.95;
}

.achievement-section {
    margin-bottom: 30px;
}

.achievement-section h4 {
    font-size: 1.5em;
    margin-bottom: 15px;
    font-weight: 700;
}

.achievements {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.achievement {
    background: rgba(255, 255, 255, 0.2);
    border-radius: 15px;
    padding: 15px 20px;
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.3);
    font-weight: 600;
    animation: achievementPop 0.6s ease;
}

@keyframes achievementPop {
    0% { transform: scale(0); opacity: 0; }
    50% { transform: scale(1.1); opacity: 0.8; }
    100% { transform: scale(1); opacity: 1; }
}

.result-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.result-btn {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid rgba(255, 255, 255, 0.3);
    border-radius: 18px;
    padding: 18px 30px;
    font-size: 1.2em;
    color: white;
    cursor: pointer;
    transition: var(--transition);
    font-weight: 600;
    backdrop-filter: blur(10px);
    display: flex;
    align-items: center;
    gap: 10px;
    min-width: 140px;
    justify-content: center;
}

.result-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.result-btn.primary {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    border-color: #4CAF50;
}

.result-btn.secondary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
}

.result-btn.share {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    border-color: #ff9a9e;
}

.btn-icon {
    font-size: 1.2em;
}

/* 弹窗样式 */
.popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1000;
    animation: popIn 0.5s ease;
}

.popup-content {
    background: linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%);
    color: white;
    padding: 30px;
    border-radius: 20px;
    text-align: center;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.popup-content.error {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
}

.popup-icon {
    font-size: 3em;
    margin-bottom: 15px;
}

.popup-text {
    font-size: 1.5em;
    font-weight: bold;
}

/* 工具类 */
.hidden {
    display: none !important;
}

.show {
    opacity: 1 !important;
}

/* 核心动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes correctPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); box-shadow: 0 0 30px rgba(86, 171, 47, 0.6); }
    100% { transform: scale(1); }
}

@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-8px); }
    20%, 40%, 60%, 80% { transform: translateX(8px); }
}

/* 响应式设计增强 */
@media (max-width: 1024px) {
    .header-content h1 {
        font-size: 2.5em;
    }
    
    .menu-buttons {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }
    
    .progress-grid {
        grid-template-columns: 1fr;
        gap: 15px;
    }
}

@media (max-width: 768px) {
    .container {
        padding: 15px;
    }
    
    .header {
        padding: 20px;
        margin-bottom: 20px;
    }
    
    .header-content h1 {
        font-size: 2em;
    }
    
    .subtitle {
        font-size: 1em;
    }
    
    .score-container {
        flex-direction: column;
        gap: 15px;
    }
    
    .main-content {
        padding: 25px;
    }
    
    .menu-buttons {
        grid-template-columns: 1fr;
        gap: 20px;
    }
    
    .menu-btn {
        min-height: 140px;
        padding: 25px;
    }
    
    .btn-icon {
        font-size: 2.5em;
    }
    
    .btn-title {
        font-size: 1.3em;
    }
    
    .answer-options {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }
    
    .answer-btn {
        min-width: 80%;
        max-width: 300px;
    }
    
    .answer-btn.large-text {
        font-size: 1.8em;
        min-width: 80%;
    }
    
    .memory-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
        max-width: 350px;
    }
    
    .memory-card {
        font-size: 1.5em;
        min-height: 80px;
    }
    
    .game-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
        margin: -25px -25px 25px -25px;
        padding: 20px 25px;
    }
    
    .game-title h3 {
        font-size: 1.6em;
    }
    
    .game-subtitle {
        font-size: 0.9em;
    }
    
    .progress-info {
        align-items: center;
    }
    
    .picture-box {
        padding: 20px;
        margin-bottom: 15px;
    }
    
    .picture-box img {
        max-width: 250px;
        max-height: 250px;
    }
    
    .question-text {
        font-size: 1.5em;
    }
    
    .pinyin-text {
        font-size: 3em;
    }
    
    .result-buttons {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }
    
    .result-btn {
        min-width: 80%;
        max-width: 250px;
    }
    
    .result-stats {
        grid-template-columns: 1fr;
        gap: 15px;
    }
    
    .popup-content {
        margin: 0 20px;
        min-width: auto;
        max-width: calc(100vw - 40px);
    }
    
    .settings-panel {
        min-width: auto;
        max-width: calc(100vw - 40px);
        margin: 0 20px;
    }
    
    .settings-options {
        padding: 20px;
    }
    
    .setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .setting-item label {
        font-size: 1em;
    }
}

@media (max-width: 480px) {
    .header-content h1 {
        font-size: 1.8em;
    }
    
    .menu-btn {
        min-height: 120px;
        padding: 20px;
    }
    
    .btn-icon {
        font-size: 2em;
    }
    
    .btn-title {
        font-size: 1.1em;
    }
    
    .btn-description {
        font-size: 0.9em;
    }
    
    .picture-box img {
        max-width: 200px;
        max-height: 200px;
    }
    
    .pinyin-text {
        font-size: 2.5em;
    }
    
    .answer-btn.large-text {
        font-size: 1.5em;
        padding: 20px 25px;
    }
    
    .memory-grid {
        grid-template-columns: repeat(2, 1fr);
        max-width: 250px;
    }
    
    .memory-card {
        font-size: 1.3em;
        min-height: 70px;
    }
    
    #resultTitle {
        font-size: 2.5em;
    }
    
    .score-number {
        font-size: 3em;
    }
    
    .popup-text {
        font-size: 1.5em;
    }
    
    .popup-icon {
        font-size: 3em;
    }
    
    .cloud {
        font-size: 1.5em;
    }
}

/* 高对比度模式支持 */
@media (prefers-contrast: high) {
    :root {
        --text-dark: #000000;
        --background-gradient: linear-gradient(135deg, #ffffff 0%, #f0f0f0 100%);
    }
    
    .answer-btn {
        border: 3px solid #000000;
    }
    
    .menu-btn {
        border: 3px solid #000000;
    }
}

/* 减少动画偏好支持 */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
    
    .particle,
    .celebration-particles,
    .success-particles {
        display: none;
    }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    :root {
        --text-dark: #ffffff;
        --text-light: #2c3e50;
        --background-gradient: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    }
    
    body::before {
        opacity: 0.8;
    }
    
    .main-content {
        background: rgba(52, 73, 94, 0.95);
        color: white;
    }
    
    .header {
        background: rgba(52, 73, 94, 0.95);
        color: white;
    }
    
    .game-header {
        background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
    }
}

/* 打印样式 */
@media print {
    .settings-btn,
    .popup,
    .loading-overlay,
    .cloud,
    .celebration-particles,
    .success-particles {
        display: none !important;
    }
    
    body {
        background: white !important;
    }
    
    .container {
        max-width: none;
        padding: 0;
    }
    
    .main-content {
        box-shadow: none;
        border: 1px solid #ccc;
    }
}

/* 焦点可见性增强 */
button:focus-visible,
input:focus-visible,
select:focus-visible {
    outline: 3px solid var(--accent-color);
    outline-offset: 2px;
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
    .answer-btn:hover,
    .menu-btn:hover,
    .result-btn:hover {
        transform: none;
    }
    
    .answer-btn:active,
    .menu-btn:active,
    .result-btn:active {
        transform: scale(0.95);
    }
}

/* 滚动行为 */
html {
    scroll-behavior: smooth;
}

/* 选择文本样式 */
::selection {
    background: var(--accent-color);
    color: white;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: var(--accent-color);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
}

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试页面 - 快乐学汉字</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .test-container {
            background: rgba(255, 255, 255, 0.1);
            padding: 30px;
            border-radius: 20px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        .test-btn {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            border: none;
            border-radius: 15px;
            padding: 20px 30px;
            font-size: 1.2em;
            color: white;
            cursor: pointer;
            margin: 10px;
            font-weight: bold;
        }
        .test-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .status {
            margin: 20px 0;
            padding: 15px;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.2);
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🎮 学字游戏测试页面</h1>
        <div id="status" class="status">正在检查功能...</div>
        
        <button class="test-btn" onclick="testFunction1()">测试看图识字</button>
        <button class="test-btn" onclick="testFunction2()">测试拼音游戏</button>
        <button class="test-btn" onclick="testFunction3()">测试记忆游戏</button>
        <button class="test-btn" onclick="goToMainGame()">进入主游戏</button>
        
        <div id="results"></div>
    </div>

    <script>
        function updateStatus(message) {
            document.getElementById('status').innerHTML = message;
        }
        
        function testFunction1() {
            updateStatus('✅ 看图识字功能正常！');
            alert('看图识字测试成功！');
        }
        
        function testFunction2() {
            updateStatus('✅ 拼音游戏功能正常！');
            alert('拼音游戏测试成功！');
        }
        
        function testFunction3() {
            updateStatus('✅ 记忆游戏功能正常！');
            alert('记忆游戏测试成功！');
        }
        
        function goToMainGame() {
            updateStatus('🚀 正在跳转到主游戏...');
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 1000);
        }
        
        // 页面加载完成后的测试
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                updateStatus('✅ 页面加载完成，所有测试按钮可以使用！');
            }, 500);
        });
    </script>
</body>
</html>
